

 
 

@import url('https://fonts.googleapis.com/css?family=Bentham');
@import url('https://fonts.googleapis.com/css?family=Work+Sans:400,500');
@import url("https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css");
@import "compass/css3";

@font-face {
    font-family: "Neutraface Text Book";
    src: url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Book.eot") format('embedded-opentype'); /* IE9 + later */      
    src: local('NeutrafaceText-Book'),
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Book.eot?#iefix") format('embedded-opentype'), /* IE6 to IE8  */
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Book.woff") format('woff'),  /* Newer browsers */
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Book.woff2") format('woff2'),      
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Book.ttf") format('truetype'), /* Safari og iOS, Chrome, Android, Firefox and Opera except Opera Mini  */
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Book.svg") format('svg'); /*IE og iOS earlier than version 5*/             
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Neutraface Text Bold";
    src: url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Bold.eot") format('embedded-opentype'); /* IE9 + later */      
    src: local('NeutrafaceText-Bold'),
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Bold.eot?#iefix") format('embedded-opentype'), /* IE6 to IE8  */
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Bold.woff") format('woff'),  /* Newer browsers */
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Bold.woff2") format('woff2'),      
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Bold.ttf") format('truetype'), /* Safari og iOS, Chrome, Android, Firefox and Opera except Opera Mini  */
         url("https://www.rymanhealthcare.co.nz/hubfs/Custom%20fonts/NeutrafaceText-Bold.svg") format('svg'); /*IE og iOS earlier than version 5*/             
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/***********************************************/
/* CSS @imports must be at the top of the file */
/* Add them above this section                 */
/***********************************************/

/****************************************/
/* HubSpot Style Boilerplate            */
/****************************************/

/* These includes are optional, but helpful. */
/* Images */

img {
    max-width: 100%;
    border: 0;
    -ms-interpolation-mode: bicubic;
    vertical-align: bottom; /* Suppress the space beneath the baseline */
}

/* Videos */

video {
    max-width: 100%;
    height: auto;
}

/* Embed Container (iFrame, Object, Embed) */

.hs-responsive-embed {
    position: relative;
    height: auto;
    overflow: hidden;
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
}

.hs-responsive-embed iframe, .hs-responsive-embed object, .hs-responsive-embed embed {
    width: 100%;
    height: 100%;
    border: 0;
}

.hs-responsive-embed,
.hs-responsive-embed.hs-responsive-embed-youtube,
.hs-responsive-embed.hs-responsive-embed-wistia,
.hs-responsive-embed.hs-responsive-embed-vimeo {
    padding-bottom: 2%;
}

.hs-responsive-embed.hs-responsive-embed-instagram {
    padding-bottom: 116.01%;
}

.hs-responsive-embed.hs-responsive-embed-pinterest {
    height: auto;
    overflow: visible;
    padding: 0;
}

.hs-responsive-embed.hs-responsive-embed-pinterest iframe {
    position: static;
    width: auto;
    height: auto;
}

iframe[src^="http://www.slideshare.net/slideshow/embed_code/"] {
    width: 100%;
    max-width: 100%;
}

@media (max-width: 568px) {
    iframe {
        max-width: 100%;
    }
}

/* Forms */

textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
select {
    padding: 6px;
    display: inline-block;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

/* Menus */

.hs-menu-wrapper ul {
    padding: 0;
}

.hs-menu-wrapper.hs-menu-flow-horizontal ul {
    list-style: none;
    margin: 0;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul {
    display: inline-block;
    margin-bottom: 20px;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul:before {
    content: " ";
    display: table;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul:after {
    content: " ";
    display: table;
    clear: both;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-menu-depth-1 {
    float: left;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li a {
    display: inline-block;
    padding: 10px 20px;
    white-space: nowrap;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children {
    position: relative;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper {
    visibility: hidden;
    opacity: 0;
    -webkit-transition: opacity 0.4s;
    position: absolute;
    z-index: 10;
    left: 0;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li a {
    display: block;
    white-space: nowrap;
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li.hs-item-has-children ul.hs-menu-children-wrapper {
    left: 180px;
    top: 0;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children:hover > ul.hs-menu-children-wrapper {
    opacity: 1;
    visibility: visible;
}

.row-fluid-wrapper:last-child .hs-menu-wrapper.hs-menu-flow-horizontal > ul {
    margin-bottom: 0;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch {
    position: relative;
    margin-bottom: 20px;
    min-height: 7em;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul {
    margin-bottom: 0;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children {
    position: static;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children ul.hs-menu-children-wrapper {
    display: none;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper {
    display: block;
    visibility: visible;
    opacity: 1;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper:before {
    content: " ";
    display: table;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper:after {
    content: " ";
    display: table;
    clear: both;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper > li {
    float: left;
}

.hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper > li a {
    display: inline-block;
    padding: 10px 20px;
    white-space: nowrap;
    max-width: 140px;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    text-decoration: none;
}

.hs-menu-wrapper.hs-menu-flow-vertical {
    width: 100%;
}

.hs-menu-wrapper.hs-menu-flow-vertical ul {
    list-style: none;
    margin: 0;
}

.hs-menu-wrapper.hs-menu-flow-vertical li a {
    display: block;
    white-space: nowrap;
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hs-menu-wrapper.hs-menu-flow-vertical > ul {
    margin-bottom: 20px;
}

.hs-menu-wrapper.hs-menu-flow-vertical > ul li.hs-menu-depth-1 > a {
    width: auto;
}

.hs-menu-wrapper.hs-menu-flow-vertical > ul li a {
    padding: 10px 20px;
    text-decoration: none;
}

.hs-menu-wrapper.hs-menu-flow-vertical > ul li.hs-item-has-children {
    position: relative;
}

.hs-menu-wrapper.hs-menu-flow-vertical > ul li.hs-item-has-children ul.hs-menu-children-wrapper {
    visibility: hidden;
    opacity: 0;
    -webkit-transition: opacity 0.4s;
    position: absolute;
    z-index: 10;
    top: 0;
    left: 180px;
}

.hs-menu-wrapper.hs-menu-flow-vertical > ul li.hs-item-has-children:hover > ul.hs-menu-children-wrapper {
    opacity: 1;
    visibility: visible;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch li a {
    min-width: 140px;
    width: auto;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch > ul {
    max-width: 100%;
    overflow: hidden;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch > ul li.hs-item-has-children ul.hs-menu-children-wrapper {
    display: none;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper,
.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper > li.hs-item-has-children > ul.hs-menu-children-wrapper {
    display: block;
    visibility: visible;
    opacity: 1;
    position: static;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch li.hs-menu-depth-2 > a {
    padding-left: 4em;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch li.hs-menu-depth-3 > a {
    padding-left: 6em;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch li.hs-menu-depth-4 > a {
    padding-left: 8em;
}

.hs-menu-wrapper.hs-menu-flow-vertical.hs-menu-show-active-branch li.hs-menu-depth-5 > a {
    padding-left: 140px;
}

.hs-menu-wrapper.hs-menu-not-show-active-branch li.hs-menu-depth-1 ul {
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.18);
    -webkit-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.18);
    -moz-box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.18);
}

@media (max-width: 568px) {
    .hs-menu-wrapper, .hs-menu-wrapper * {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        display: block;
        width: 100%;
    }

    .hs-menu-wrapper.hs-menu-flow-horizontal ul {
        list-style: none;
        margin: 0;
        display: block;
    }

    .hs-menu-wrapper.hs-menu-flow-horizontal > ul {
        display: block;
    }

    .hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-menu-depth-1 {
        float: none;
    }

    .hs-menu-wrapper.hs-menu-flow-horizontal > ul li a,
    .hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li a,
    .hs-menu-wrapper.hs-menu-flow-horizontal.hs-menu-show-active-branch > ul li.hs-item-has-children.active-branch > ul.hs-menu-children-wrapper > li a {
        display: block;
        max-width: 100%;
        width: 100%;
    }
}

.hs-menu-wrapper.hs-menu-flow-vertical.no-flyouts > ul li.hs-item-has-children ul.hs-menu-children-wrapper,
.hs-menu-wrapper.hs-menu-flow-horizontal.no-flyouts > ul li.hs-item-has-children ul.hs-menu-children-wrapper {
    visibility: visible;
    opacity: 1;
}

/* Space Module */

.widget-type-space {
    visibility: hidden;
}

/* Blog Author Section */

.hs-author-listing-header {
    margin: 0 0 .75em 0;
}

.hs-author-social-links {
    display: inline-block;
}

.hs-author-social-links a.hs-author-social-link {
    width: 24px;
    height: 24px;
    border-width: 0px;
    border: 0px;
    line-height: 24px;
    background-size: 24px 24px;
    background-repeat: no-repeat;
    display: inline-block;
    text-indent: -99999px;
}

.hs-author-social-links a.hs-author-social-link.hs-social-facebook {
    background-image: url("//static.hubspot.com/final/img/common/icons/social/facebook-24x24.png");
}

.hs-author-social-links a.hs-author-social-link.hs-social-linkedin {
    background-image: url("//static.hubspot.com/final/img/common/icons/social/linkedin-24x24.png");
}

.hs-author-social-links a.hs-author-social-link.hs-social-twitter {
    background-image: url("//static.hubspot.com/final/img/common/icons/social/twitter-24x24.png");
}

.hs-author-social-links a.hs-author-social-link.hs-social-google-plus {
    background-image: url("//static.hubspot.com/final/img/common/icons/social/googleplus-24x24.png");
}

/*
 * jQuery FlexSlider v2.0
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: Tyler Smith (@mbmufffin)
 */

/* Wrapper */
.hs_cos_wrapper_type_image_slider {
    display: block;
    overflow: hidden
}

/* Browser Resets */
.hs_cos_flex-container a:active,
.hs_cos_flex-slider a:active,
.hs_cos_flex-container a:focus,
.hs_cos_flex-slider a:focus  {outline: none;}
.hs_cos_flex-slides,
.hs_cos_flex-control-nav,
.hs_cos_flex-direction-nav {margin: 0; padding: 0; list-style: none;}

/* FlexSlider Necessary Styles
*********************************/
.hs_cos_flex-slider {margin: 0; padding: 0;}
.hs_cos_flex-slider .hs_cos_flex-slides > li {display: none; -webkit-backface-visibility: hidden; position: relative;} /* Hide the slides before the JS is loaded. Avoids image jumping */
.hs_cos_flex-slider .hs_cos_flex-slides img {width: 100%; display: block; border-radius: 0px;}
.hs_cos_flex-pauseplay span {text-transform: capitalize;}

/* Clearfix for the .hs_cos_flex-slides element */
.hs_cos_flex-slides:after {content: "."; display: block; clear: both; visibility: hidden; line-height: 0; height: 0;}
html[xmlns] .hs_cos_flex-slides {display: block;}
* html .hs_cos_flex-slides {height: 1%;}

/* No JavaScript Fallback */
/* If you are not using another script, such as Modernizr, make sure you
 * include js that eliminates this class on page load */

/* FlexSlider Default Theme
*********************************/
.hs_cos_flex-slider {margin: 0 0 60px; background: #fff; border: 0; position: relative; -webkit-border-radius: 4px; -moz-border-radius: 4px; -o-border-radius: 4px; border-radius: 4px; zoom: 1;}
.hs_cos_flex-viewport {max-height: 2000px; -webkit-transition: all 1s ease; -moz-transition: all 1s ease; transition: all 1s ease;}
.loading .hs_cos_flex-viewport {max-height: 300px;}
.hs_cos_flex-slider .hs_cos_flex-slides {zoom: 1;}

.carousel li {margin-right: 5px}


/* Direction Nav */
.hs_cos_flex-direction-nav {*height: 0;}
.hs_cos_flex-direction-nav a {width: 30px; height: 30px; margin: -20px 0 0; display: block; background: url(//static.hsappstatic.net/content_shared_assets/static-1.3935/img/bg_direction_nav.png) no-repeat 0 0; position: absolute; top: 50%; z-index: 10; cursor: pointer; text-indent: -9999px; opacity: 0; -webkit-transition: all .3s ease;}
.hs_cos_flex-direction-nav .hs_cos_flex-next {background-position: 100% 0; right: -36px; }
.hs_cos_flex-direction-nav .hs_cos_flex-prev {left: -36px;}
.hs_cos_flex-slider:hover .hs_cos_flex-next {opacity: 0.8; right: 5px;}
.hs_cos_flex-slider:hover .hs_cos_flex-prev {opacity: 0.8; left: 5px;}
.hs_cos_flex-slider:hover .hs_cos_flex-next:hover, .hs_cos_flex-slider:hover .hs_cos_flex-prev:hover {opacity: 1;}
.hs_cos_flex-direction-nav .hs_cos_flex-disabled {opacity: .3!important; filter:alpha(opacity=30); cursor: default;}

/* Direction Nav for the Thumbnail Carousel */
.hs_cos_flex_thumbnavs-direction-nav {
    margin: 0px;
    padding: 0px;
    list-style: none;
    }
.hs_cos_flex_thumbnavs-direction-nav {*height: 0;}
.hs_cos_flex_thumbnavs-direction-nav a {width: 30px; height: 140px; margin: -60px 0 0; display: block; background: url(//static.hsappstatic.net/content_shared_assets/static-1.3935/img/bg_direction_nav.png) no-repeat 0 40%; position: absolute; top: 50%; z-index: 10; cursor: pointer; text-indent: -9999px; opacity: 1; -webkit-transition: all .3s ease;}
.hs_cos_flex_thumbnavs-direction-nav .hs_cos_flex_thumbnavs-next {background-position: 100% 40%; right: 0px; }
.hs_cos_flex_thumbnavs-direction-nav .hs_cos_flex_thumbnavs-prev {left: 0px;}
.hs-cos-flex-slider-control-panel img { cursor: pointer; }
.hs-cos-flex-slider-control-panel img:hover { opacity:.8; }
.hs-cos-flex-slider-control-panel { margin-top: -30px; }



/* Control Nav */
.hs_cos_flex-control-nav {width: 100%; position: absolute; bottom: -40px; text-align: center;}
.hs_cos_flex-control-nav li {margin: 0 6px; display: inline-block; zoom: 1; *display: inline;}
.hs_cos_flex-control-paging li a {width: 11px; height: 11px; display: block; background: #666; background: rgba(0,0,0,0.5); cursor: pointer; text-indent: -9999px; -webkit-border-radius: 20px; -moz-border-radius: 20px; -o-border-radius: 20px; border-radius: 20px; box-shadow: inset 0 0 3px rgba(0,0,0,0.3);}
.hs_cos_flex-control-paging li a:hover { background: #333; background: rgba(0,0,0,0.7); }
.hs_cos_flex-control-paging li a.hs_cos_flex-active { background: #000; background: rgba(0,0,0,0.9); cursor: default; }

.hs_cos_flex-control-thumbs {margin: 5px 0 0; position: static; overflow: hidden;}
.hs_cos_flex-control-thumbs li {width: 25%; float: left; margin: 0;}
.hs_cos_flex-control-thumbs img {width: 100%; display: block; opacity: .7; cursor: pointer;}
.hs_cos_flex-control-thumbs img:hover {opacity: 1;}
.hs_cos_flex-control-thumbs .hs_cos_flex-active {opacity: 1; cursor: default;}

@media screen and (max-width: 860px) {
  .hs_cos_flex-direction-nav .hs_cos_flex-prev {opacity: 1; left: 0;}
  .hs_cos_flex-direction-nav .hs_cos_flex-next {opacity: 1; right: 0;}
}

.hs_cos_flex-slider .caption {
    background-color: rgba(0,0,0,0.5);
    position: absolute;
    font-size: 2em;
    line-height: 1.1em;
    color: white;
    padding: 0px 5% 0px 5%;
    width: 100%;
    bottom: 0;
    text-align: center;
}

.hs_cos_flex-slider .superimpose .caption {
    color: white;
    font-size: 3em;
    line-height: 1.1em;
    position: absolute;
    padding: 0px 5% 0px 5%;
    width: 90%;
    top: 40%;
    text-align: center;
    background-color: transparent;
}

@media all and (max-width: 400px) {
    .hs_cos_flex-slider .superimpose .caption {
        background-color: black;
        position: static;
        font-size: 2em;
        line-height: 1.1em;
        color: white;
        width: 90%;
        padding: 0px 5% 0px 5%;
        top: 40%;
        text-align: center;
    }

    /* beat recaptcha into being responsive, !importants and specificity are necessary */
    #recaptcha_area table#recaptcha_table {width: 300px !important;}
    #recaptcha_area table#recaptcha_table .recaptcha_r1_c1 {width: 300px !important;}
    #recaptcha_area table#recaptcha_table .recaptcha_r4_c4 { width: 67px !important;}
    #recaptcha_area table#recaptcha_table #recaptcha_image {width:280px !important;}
}

.hs_cos_flex-slider h1,
.hs_cos_flex-slider h2,
.hs_cos_flex-slider h3,
.hs_cos_flex-slider h4,
.hs_cos_flex-slider h5,
.hs_cos_flex-slider h6,
.hs_cos_flex-slider p {
    color: white;
}

/* Thumbnail only version of the gallery */
.hs-gallery-thumbnails li {
    display: inline-block;
    margin: 0px;
    padding: 0px;
    margin-right:-4px;
}
.hs-gallery-thumbnails.fixed-height li img {
    max-height: 150px;
    margin: 0px;
    padding: 0px;
    border-width: 0px;
}


/* responsive pre elements */

pre {
    overflow-x: auto;
}

/* responsive pre tables */

table pre {
    white-space: pre-wrap;
}

/* adding minimal spacing for blog comments */
.comment {
    margin: 10px 0 10px 0;
}

/* make sure lines with no whitespace don't interefere with layout */
.hs_cos_wrapper_type_rich_text,
.hs_cos_wrapper_type_text,
.hs_cos_wrapper_type_header,
.hs_cos_wrapper_type_section_header,
.hs_cos_wrapper_type_raw_html,
.hs_cos_wrapper_type_raw_jinja,
.hs_cos_wrapper_type_page_footer {
    word-wrap: break-word;
}
/* HTML 5 Reset */

article, aside, details, figcaption, figure, footer, header, hgroup, nav, section {
    display: block;
}

audio, canvas, video {
    display: inline-block;
    *display: inline;
    *zoom: 1;
}

audio:not([controls]) {
    display: none;
}

/* Support migrations from wordpress */

.wp-float-left {
    float: left;
    margin: 0 20px 20px 0;
}

.wp-float-right {
    float: right;
    margin: 0 0 20px 20px;
}

/* Responsive Google Maps */

#map_canvas img, .google-maps img {
    max-width: none;
}

/* line height fix for reCaptcha theme */
#recaptcha_table td {line-height: 0;}
.recaptchatable #recaptcha_response_field {min-height: 0;line-height: 12px;}


/*****************************************/
/* Start your style declarations here    */
/*****************************************/





/*--------- SIZING ----------*/

 



/*--------- NAVBAR COLORS ----------*/

 
 


 
 

 /* added */


/*--------- Theme Base Colors ----------*/

 

 
 
 
 
 


body{
    padding:0;
    font-size: 16px ;
    line-height: 1.4;
    overflow-x:hidden;
    -webkit-font-smoothing: antialiased;
    font-family: 'Work Sans', sans-serif; ;
    width:100%;
    letter-spacing:.25px;
    background: #fff ;
    margin:0 auto;
    max-width:100%;
    color:#191919 ;
}

.row-fluid [class*="span"].wrapper,
.row-fluid .wrapper{
    width: 100%;
    max-width: 1400px;
    margin-left:auto;
    margin-right:auto;
    float: none;
    padding-left:15px;
    padding-right:15px;
}

.row-fluid .wrapper.city-villages {
    max-width: 1150px;
}

.row-fluid .wrapper.wrapper-narrow {
    max-width: 800px;
}


h1,h2,h3,h4,h5,h6,p,a {
    text-transform:initial;
    margin:0;
    padding:0;
    color:#191919 ;
    line-height: 1.5;
}

h1,h2 {
    color:#114C8F ; /* changed */
    font-family:  'Neutraface Text Bold', sans-serif; ; /* changed */
    font-weight: 600; /* changed */
    text-transform: uppercase; /* added */
    letter-spacing: -0.5px; /* added */
}

h3,h4,h5 {
    color:#114C8F ; /* changed */    
    font-family:  'Neutraface Text Bold', sans-serif; ;
    font-weight: 500; /* changed */
    line-height: 1.2;
    margin-bottom: 0.5em; /* changed */
}

.body-container-wrapper ul,.body-container-wrapper li{
    margin-bottom:15px;
}

a {
    color: #191919 ;
    
}

h1 {
    font-size:3rem;
    line-height: 1; /* changed */
}

h2 {
    font-size:2.25rem; /* changed */
    line-height: 1.1;
    margin-bottom: 0.25em;
}

h3 {
    font-size: 1.7rem; /* changed */
}

h4 {
    font-size: 1.2rem; /* changed */
}

h5 {
    font-size: 0.75rem;
}

p {
    font-size: 16px;
    line-height: 1.4;
    letter-spacing: .25px;
    font-weight:400;
    margin-bottom: 1em;
}

.body-container-wrapper li{
    font-size: 16px;
    line-height: 1.6;
    letter-spacing: .25px;
    font-weight:400;
}

.body-container-wrapper {
    background:#fff;
    overflow:hidden;
    position:relative;
    z-index:2;
    //padding-top:85px;
}

.body-container-wrapper ul{
    padding:0;
    text-decoration:none;
    margin:20px 0;
}

.body-container-wrapper ol{
    margin:0;
    padding:0;
    text-decoration:none;
    margin-bottom:15px;    
}

.body-container-wrapper li{
    margin-left:26px;
    margin-bottom:8px;
}
/* CE- additional CSS for wrapping*/
.content-wrapper {
  padding-top: 40px;
  padding-bottom: 40px;
}

.body-container-wrapper ol li:before{
    display:none;
}

.no-list-style ul{
    margin:0;    
    list-style:none;
}

.no-list-style li{
    margin:0;
}

.body-container-wrapper a{
    color:#F46419;
    transition: all ease-in-out .2s;
}

.body-container-wrapper .breadcrumb-menu li{
    margin-left: 0px;
}

.body-container-wrapper .breadcrumb-menu a{
    color: inherit;
}

/*Contrast colors*/

.background-grey {
  background-color: #E5E5E5 ;
}

.background-dk-blue {
  background-color: #0d4487 ;
}


blockquote {
 margin: 0; 
 padding-left: 17px;
 
 border-left: 5px solid #F46419;
   margin-left: 4px;
   margin-top: 25px;
   margin-bottom: 20px;
}
blockquote:before {
 display: none;
}
blockquote:not(:first-of-type) {
    padding-right: 30px;
    margin-right: 10px;
}
blockquote p {
 color: #a9a9a9;
 font-size: 1rem;
 line-height: 1.4;
 font-style: italic;
 font-weight: 300;
 letter-spacing: 0.25px;
}

blockquote:nth-of-type(even) {
 text-align: right;
 border-left: none;
 border-right: 5px solid #F46419;
 margin-right: 40px;
}

@element 'blockquote' and (min-width: 300px) {
 blockquote {
   padding: 1em 20% 1em 1em;
 }
 blockquote p {
   font-size: 14pt;
 }
 blockquote:nth-of-type(even) {
   padding: 1em 1em 1em 20%;
   margin-right:none;
 }
}

div#blockquote-icon {
 background-color:#FDF5EB !important;
 border:none !important;
 margin: 1rem 1rem 2rem;
 padding: 1.5rem 1.2rem;
}
div#blockquote-icon p::before {
    font-family: FontAwesome;
    content: "\f10d";
    color: #F2A628;
    font-size: 4em;
    font-style: italic;
    font-weight: 700;
    opacity: .2;
    position: absolute;
    top: -.1em;
    left:auto;
    text-shadow: none;
    z-index: 300;
}


.widget-type-cta{
    margin-top:15px;
}

.button-wrapper{
  max-width: 300px;
  display: block;
  margin: 0 auto !important;
}

.view-more-button {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
  font-family: ;  
  background-color: #F46419 ;
  color: #fff;
  text-align: center;
  font-weight: 600;
  padding: 10px 30px;
  display: inline-block;
  text-decoration: none;
  width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;
  max-width: 16em;
  margin-bottom: 1em;
  margin-left: auto;
  margin-right: auto;
  border: none;
  text-transform: uppercase; /* added */

}

.view-more-button:hover {
  background-color: #F46419 ;
}

.view-more-button:focus {
  outline: none;
}

a.orange-btn {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
  font-family: inherit;  
  background-color: transparent; /* changed */
  color: #F46419  !important; /* changed */
  text-align: center;
  font-weight: 600;
  padding: 15px 30px; /* changed */
  display: inline-block;
  text-decoration: none;
  width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;
  max-width: 16em;
  margin-bottom: 1em;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #F46419 ; /* added */
  text-transform: uppercase; /* added */
}
a.orange-btn:hover {
  background-color: #F46419 ;
  color: #fff !important;  /* added */
}

.orange-btn.large-btn {
  font-size: 1.2em;
  max-width: 20em !important;
  padding: 20px 30px;
  line-height: 1.3;
  margin-bottom: 0;
}

.orange-btn.white-border-btn {  
  max-width: 20em !important;  
  line-height: 1.3;
  margin-bottom: 0;
  border: 2px solid #fff;
  background-color:transparent;
  color: #fff !important; /* added */
}

a.orange-btn.white-border-btn:hover {
  background-color: #fff; /* added */
  color: #F46419  !important;  /* added */
}

.orange-btn.dark-border-btn {  
  max-width: 20em !important;  
  line-height: 1.3;
  margin-bottom: 0;
  border: 2px solid #191919;
  background-color:#fff;
  color: #191919; /* added */
}

/* don't need this anymore but need to remove it where it has been used */
.orange-btn-reverse {
  max-width: 16em !important;
  background-color: transparent;
  border: 2px solid #F46419; /* changed */
  color: #F46419; /* changed */
  font-weight: 600;
  padding: 15px 30px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  width: 90%;
}

.orange-btn-reverse:hover {
  background-color: #F46419; /* changed */
  color: #fff;
}

.white-btn {
  max-width: 16em !important;
  background-color: transparent;
  border: 1px solid #191919;
  color: #191919 !important;
  font-weight: 400;
  padding: 15px 30px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  width: 90%;
}

.white-btn:hover {
  background-color: #F46419; /* changed */
  color: #fff !important;
  border: 1px solid #F49419; /* changed */
}



.hs_cos_wrapper_type_custom_widget a.cta_button{
    margin-top:25px;
}

body a.cta_button.image-cta{
    padding:0;
    background:transparent;
    text-align:center;
}

body a.cta_button:hover,
body .container-fluid .row-fluid .hs-button:hover{
/*     box-shadow: 0px 8px 20px 2px rgba(0, 0, 0, 0.32); */
    top:-5px; 
    background-color: #F46419 ; /* changed */
    color: #fff; /* changed */
} 

/*body .white-cta a.cta_button,
body .all-text-white a.cta_button,
body .white-cta .container-fluid .row-fluid .hs-button{    
    background:#fff;
    border-color:#fff;
    color:#000;
}*/

body .dark-cta a.cta_button{
    background:#0d2e4f;
    border-color:#0d2e4f;
}

.simple-cta a.cta_button,
.simple-cta a{
    background:transparent;
    border:0;
    padding:0;
    position:relative;
    color: #F46419;
    font-weight:400;
}

.simple-cta a:after{
    content:'\f107';
    font-family: FontAwesome;
    padding-left: 6px;
}   

.align-center {
    text-align:center;
}

.align-left {
    text-align:left;
}

.align-right {
    text-align:right;
}

.float-left{
    float:left;
}

.float-right{
    float:right;
}

.overlay .base-color-overlay{
    position:relative;
    z-index:1;
}

.overlay:before{
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background:rgba(0, 0, 0, 0.6);
    z-index: -1;
}
/*
@media (min-width:1025px){

.left-triangle,
.right-triangle{
    position:relative;
    z-index:1;
    overflow:hidden
}

.left-triangle:before{
    position: absolute;
    content: '';
    top: 15%;
    left: -200px;
    z-index: -1;
    width: 300px;
    height: 400px;
    background:#F46419;
    transform: rotate(30deg);
    opacity: .3;
}

.right-triangle:after{
    position: absolute;
    content: '';
    right: -200px;
    bottom: 15%;
    z-index: -1;
    width: 300px;
    height: 400px;
    background:#F46419;
    transform: rotate(30deg);
    opacity: .3;
}
}*/

.base-color-overlay:before{
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background:#F46419;
    z-index: -1;
    opacity:.95;
    background: ;
}

.grey-bg{
    background:#f8f8f8;
}

body .row-fluid .blue-bg{
    background: #0d82df;
}


body .container-fluid .row-fluid .section-heading.align-center {
    max-width: 840px;
    float: none;
    margin-left:auto;
    margin-right:auto;
    margin-bottom:60px;
}

.section-heading h2{
    margin-bottom: 1em;
}

.section-heading p{
    font-size: 18px;
    font-weight: 400;
    max-width: 600px;
    margin: 0 auto;
    letter-spacing: .5px;
}

.section-heading h5{
    font-size: 14px;
    font-weight:700;
    letter-spacing:0.25px;
    text-transform: uppercase;
    color:#F46419;
    font-family: 'Montserrat',sans-serif;
    margin-bottom: 5px;
}

.all-text-white,
.all-text-white h1,
.all-text-white h2,
.all-text-white h3,
.all-text-white p,
.all-text-white h4,
.all-text-white h5,
.all-text-white h6,
.all-text-white li,
.all-text-white .fa,
.all-text-white a{
    color:#fff;
}

.text-margin-zero h1,
.text-margin-zero h2,
.text-margin-zero h3,
.text-margin-zero p,
.text-margin-zero h4,
.text-margin-zero h5,
.text-margin-zero h6,
.text-margin-zero .fa,
.text-margin-zero a{
    margin:0;
}

.base-color-text h1,
.base-color-text h2,
.base-color-text h3,
.base-color-text p,
.base-color-text h4,
.base-color-text h5,
.base-color-text h6,
.base-color-text .fa,
.base-color-text a{
    color:#F46419;
}

.body-container-wrapper .all-text-white li:before{
    background:#fff;
}

body .container-fluid .row-fluid .pad-bottom{
    margin-bottom:80px;
}

body .container-fluid .row-fluid [class*="span"]{
    min-height:0;
}

.section-bg{
    background-size: cover!important;
    background-position: center center!important;
}

.section-bg .bg-image{
    display:none!important;
}

.section-padding{
    padding:3rem 0;
}

.section-border{
    border-bottom:1px solid #e7e7e7;
}

.cta-margin{
    margin-top:50px;
}

@media (min-width: 768px) {
    
.equal-height-container > .row-fluid-wrapper > .row-fluid {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
}

body .row-fluid .vertical-center{
    display: flex;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    justify-content: center;
    -webkit-justify-content: center;
    justify-content: center;
}     
}

.inner-page-hero-section{
    padding:30px 0;
}

.inner-page-hero-section h1{
    margin:0;
    font-size:30px;
}

.village-logo {
    width: auto;
    height: auto;
    max-height: 150px;
    max-width: 250px;
    margin-bottom: 1em;
}

.village-logo img{
    width: auto !important;
    height: auto;
    max-height: 150px;
    max-width: 250px;
}

.namesake-logo {
    width: auto;
    height: auto;
    max-width: 250px;
    margin-bottom: 1em;
    text-align: center;
}

@media (max-width: 768px) {
.mobile-rich-image img {
    max-width: 100%;
    border: 0;
    -ms-interpolation-mode: bicubic;
    vertical-align: bottom;
    float: none !important;
    display: block;
    text-align: center;
    margin: 0 auto 2em;
}
}

@media (max-width: 640px) {
.mobile-hide {
  display: none !important;
}
}

@media (min-width: 640px) {
.mobile-view {
  display: none !important;
}
}

/*========================================================
        Gallery Styling
========================================================*/

.caption.below{
    background-color: #FFFFFF;
    text-align: center !important;
    padding: 1em;
}

/*========================================================
        Border Styling
========================================================*/
.border-table{
 border: 1px solid #191919;
  
}
.border-row-bottom{
 border-bottom: 1px solid #191919;
  padding:1em;
}
.border-row-top{
 border-top: 1px solid #191919;
}

/*========================================================
        Padding Styling
========================================================*/

.pt10 { padding-top: 10px; }
.pt20 { padding-top: 20px; }
.pt30 { padding-top: 30px; }
.pt40 { padding-top: 40px; }
.pt50 { padding-top: 50px; }
.pt150 { padding-top: 150px; }

.pr10 { padding-right: 10px; }
.pr20 { padding-right: 20px; }
.pr30 { padding-right: 30px; }
.pr40 { padding-right: 40px; }
.pr50 { padding-right: 50px; }

.pb10 { padding-bottom: 10px; }
.pb20 { padding-bottom: 20px; }
.pb30 { padding-bottom: 30px; }
.pb40 { padding-bottom: 40px; }
.pb50 { padding-bottom: 50px; }

.pl10 { padding-left: 10px; }
.pl20 { padding-left: 20px; }
.pl30 { padding-left: 30px; }
.pl40 { padding-left: 40px; }
.pl50 { padding-left: 50px; }

/*========================================================
        Responsive tables for modules Styling
========================================================*/

/* Tables
================================== */
.Rtable {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  justify-content: center;
  position: relative;
}
.Rtable-alt {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 3em;
  padding: 0;
  justify-content: center;
  position: relative;
}
.Rtable-cell {
  box-sizing: border-box;
  flex-grow: 1;
  width: 100%;
  overflow: hidden;
  list-style: none;
  padding: 0.25em;
  border-color: 5px solid #fff;
}

/* Table column sizing
================================== */
.Rtable--1cols > .Rtable-cell {
  width: 100%;
}
.Rtable--2cols > .Rtable-cell {
  width: 50%;
}
.Rtable--3cols > .Rtable-cell {
  width: 30.33%;
}
.Rtable--4cols > .Rtable-cell {
  width: 24%;
}
.Rtable--5cols > .Rtable-cell {
  width: 20%;
}
.Rtable--6cols > .Rtable-cell {
  width: 15%;
}

/* Cell styles
================================== */

.Rtable-cell-wide {
  width: 65%;
}

.Rtable-cell-narrow {
  width: 35%;
}

// Individual cell sizing
.Rtable-cell.Rtable-cell-1of1  { width: 100%; }
.Rtable-cell.Rtable-cell-5of6  { width: 83.33%; }
.Rtable-cell.Rtable-cell-4of5  { width: 80%; }
.Rtable-cell.Rtable-cell-3of4  { width: 75%; }
.Rtable-cell.Rtable-cell-2of3  { width: 66.66%; }
.Rtable-cell.Rtable-cell-3of5  { width: 60%; }
.Rtable-cell.Rtable-cell-1of2  { width: 50%; }
.Rtable-cell.Rtable-cell-2of4  { width: 40%; }
.Rtable-cell.Rtable-cell-1of3  { width: 33.33%; }
.Rtable-cell.Rtable-cell-1of4  { width: 25%; }
.Rtable-cell.Rtable-cell-1of5  { width: 20%; }
.Rtable-cell.Rtable-cell-1of6  { width: 15%; }


/* Responsive
==================================== */
@media (max-width:1025px) {
  .Rtable--6cols > .Rtable-cell {
    width: 25%;
  }
  .Rtable--4cols > .Rtable-cell {
    width: 32%;
  } 
}

@media (max-width:768px){
.Rtable-cell.Rtable-cell-3of4  { width: 50%; }
.Rtable-cell.Rtable-cell-1of4  { width: 45%; }  
.Rtable-cell.Rtable-cell-2of3  { width: 50%; }
.Rtable-cell.Rtable-cell-1of3  { width: 45%; }
  
  .Rtable--3cols > .Rtable-cell, .Rtable--4cols > .Rtable-cell {
  width: 50%;
  }
  .Rtable--4cols > .Rtable-cell {
    width: 48%;
  }   
  .Rtable--6cols > .Rtable-cell {
    width: 33%;
  }  
  .Rtable-cell-wide {
  width: 50%;
  }
  .Rtable-cell-narrow {
  width: 50%;
  } 
}  
@media (max-width:590px){
  .Rtable--2cols > .Rtable-cell {
    width: 100%;
  }  
  .Rtable--3cols > .Rtable-cell{
  width: 100% ;
  }
  .Rtable--4cols > .Rtable-cell {
    width: 100%;
  }  
  .Rtable--6cols > .Rtable-cell {
    width: 50%;
  }
.Rtable-cell.Rtable-cell-2of3  { width: 100%; }
.Rtable-cell.Rtable-cell-1of3  { width: 100%; }  
}
  
@media all and (max-width: 640px) {

  .Rtable-cell {
  text-align: center !important;
  }  
  .Rtable--collapse {
    display: block;
  }
  .Rtable--collapse > .Rtable-cell {
    width: 100% !important;
  }
  .Rtable--collapse > .Rtable-cell--foot {
    margin-bottom: 1em;
  }
  .Rtable-cell-wide {
  width: 100%;
  }
  .Rtable-cell-narrow {
  width: 100%;
  }
}
  
.no-flexbox .Rtable {
  display: block;
}
.no-flexbox .Rtable > .Rtable-cell {
  width: 100%;
}
.no-flexbox .Rtable > .Rtable-cell--foot {
  margin-bottom: 1em;
}

.no-flexbox .Rtable-alt {
  display: block;
}
.no-flexbox .Rtable-alt > .Rtable-cell {
  width: 100%;
}
.no-flexbox .Rtable-alt > .Rtable-cell--foot {
  margin-bottom: 1em;
}

/* Vmiddle used for virtual tour
   ========================================================================== */

/*Vertically center two or more grid columns to align them perfectly inline. */

.vmiddle > .row-fluid-wrapper > .row-fluid {
    display: flex;
    align-items: center;
    justify-content: center;
}

.vmiddle > .row-fluid-wrapper > .row-fluid:before,
.vmiddle > .row-fluid-wrapper > .row-fluid:after {
    display: none;
}

.vmiddle.shift-col > .row-fluid-wrapper > .row-fluid {
    justify-content: space-between;
}

@media (max-width: 768px) {

    .vmiddle.md-stack > .row-fluid-wrapper > .row-fluid {
        display: block;
    }

    .vmiddle.md-stack > .row-fluid-wrapper > .row-fluid:before,
    .vmiddle.md-stack > .row-fluid-wrapper > .row-fluid:after {
        display: table;
    }
}

@media (max-width: 640px) {

    .vmiddle:not(.never-stack) > .row-fluid-wrapper > .row-fluid {
        display: block;
    }

    .vmiddle:not(.never-stack) > .row-fluid-wrapper > .row-fluid:before,
    .vmiddle:not(.never-stack) > .row-fluid-wrapper > .row-fluid:after {
        display: table;
    }
}

/*========================================================
        Responsive images for footer
========================================================*/
.center-image-container-row::after {
  content: "";
  clear: both;
  display: table;
  }
.center-image-container {
  float: left;
  width: 50%;
    padding-top: 20px;
    padding-bottom:20px;
    padding-left:10px;
    padding-right:10px;
    height: auto;
}
.center-image-container img{
max-height: 100px;
  width:auto !important;
}

@media (max-width: 1024px)
  {
    .center-image-container-row {
      max-width:300px;
      text-align: center;
      float: right !important;;
    }
    .center-image-container {
    display: block;
    
    }
    .center-image-container img{
min-height: 120px;

}
    
  }
@media (max-width: 767px){
  .center-image-container {  
  float: left !important;
    width: 50% !important;
  }
  .center-image-container img{
height: 200px;
 
}
}
/*========================================================
        Living and Care module Styling
========================================================*/

.lc-container {
  width: 100%;
}

.lc-image-container {
  position: relative;
  max-width: 640px;
  margin: 0px auto;
  height: 450px;
  width: 100%;
  padding: 0 180px;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;    
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;  
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
  filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='.myBackground.jpg', sizingMethod='scale');
  -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='myBackground.jpg', sizingMethod='scale')";
}

.lc-image-container img{
  object-fit: cover;
  width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;  
  height: 450px;
}

.white-text-block {
  position: absolute !important;
  bottom: 5%;
  right: 5%;
  left: 5%;
  background-color: #fff;
  padding: 1em;
  width: 60%;
  text-align: left;
}

.white-text-block h5 {
  text-transform: uppercase;
  color: #F46419;
}
.white-text-block h4 {
padding-bottom:5px;
}

.lc-flex-row {
  display: table;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  align-content: stretch;
  flex-wrap: wrap;
  text-align: center;
  width: 100%;
}

.multicard-text-block {
  background-color: #fff;
  padding: 1em;
  width: 100%;
  text-align: left;
  display: flex;
  flex-flow: column nowrap;  
}

@media screen and (max-width: 1024px) {
  .multicard-call-to-action-block {
  display: block !important;
}
  .multicard-container {
  margin: 0.5em !important;
  }
}

@media (max-width:640px){
  .lc-flex-row {
  display: block;  
  }
  .multicard-container {
  margin: 0.5em 0 !important;
  }
}


/*===============================================================
        Full width Banner Image with Content module Styling
================================================================*/
.full-width-image-with-content {
 /* min-height: 300px; */
  position: relative;
  overflow-wrap: break-word;
 
}
.full-width-image-with-content img {
  width: 100%;
  object-fit: cover;
  height: auto !important;
}
.full-width-image-with-content .page-center {
  padding-right: 10px;
  padding-left: 10px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
.full-width-image-with-content .custom-content {
  position: absolute;
  max-width: 50%;
  top: auto;
  bottom:0;
  width: fit-content;
  background-color: rgba(255, 255, 255, 0.7);
  padding: 1em 4em;
  overflow-wrap: break-word;
  }

.full-width-image-with-content .custom-content.left {
  text-align: left;
  right: 0;
}

.full-width-image-with-content.center .custom-content {
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
}

.full-width-image-with-content .custom-content.center {
  text-align:  center;
}

.full-width-image-with-content .custom-content h2 {
  color: #191919;
  padding:15px 0px;
  font-size: 3em;
  line-height: 1;
}

.full-width-image-with-content .custom-content a:empty {
  display: none;
}
.full-width-image-with-content .custom-content a {
  margin-bottom: 0em; !important;
}

.full-width-image-with-content.left .custom-content.left {
  left: 0;
}
.full-width-image-with-content.left .custom-content.left {
  left: 0;
}
.full-width-image-with-content.right .custom-content {
  right: 0;
}
.full-width-image-with-content .custom-content.right {
  text-align: right;
}

@media (max-width:1360px) and (min-width:767px){
  .full-width-image-with-content img {
    position: relative;
    top: 0;
    bottom: 0;
    width: 100% ;
    object-fit: cover;
  }
  .full-width-image-with-content .page-center {
    position: static;
   
  }
  .full-width-image-with-content .custom-content {
    margin-right: 0;
    transform: none;
  }
 
  .full-width-image-with-content.left .custom-content.left {
    margin-left: 1.5em; !important
    margin-right: auto;
    position: absolute;
  }

}

@media (max-width: 991px) {
  .full-width-image-with-content .custom-content {
    max-width: 50%;
  }
  .full-width-image-with-content {
    overflow:  hidden;
  }
  }

  @media (max-width: 767px) 
  
  {
   .full-width-image-with-content .page-center {
    position: relative;
    left: auto;
    right: auto;
    top: auto;
    padding-left:0px;
    padding-right:0px; 
  }

  /* @media (max-width: 767px) */
  .hot-post-header .full-width-image-with-content .page-center {
   position: absolute !important;
   bottom: 0;
   top: auto;
   height:auto;
  }
    
  .full-width-image-with-content .custom-content {
   position: relative;
    max-width: 100%;
    top: auto;
    padding: 40px 0;
    background-color: #f0f0f0;
  }

 
    
    .full-width-image-with-content #ryman-mobile-blog-position {
    position: absolute !important;
    bottom: 0;
   }

  }

@media (min-width:767px) and (max-width:1024px)
  {
     .full-width-image-with-content .custom-content h2 {
      font-size: 3rem !important;
  }
}
  
  @media (max-width: 479px) {
   .full-width-image-with-content .custom-content h2 {
      font-size: 36px;
  }
  
}

/*===============================================
        Home Banner module Styling
=================================================*/

.full-width-image-with-content .custom-content-home {
  position: absolute;
  max-width: 45%;
  top: auto;
  width: 100%;
  bottom: 0;
  padding: 1em 2em;
  background-color: rgba(255, 255, 255, 0.9);
  left: 80px;
 }

.full-width-image-with-content .custom-content-home.left {
  text-align: left;
  }

.full-width-image-with-content.center .custom-content-home {
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
}

.full-width-image-with-content .custom-content-home .center {
  text-align:  center;
}

.full-width-image-with-content .custom-content-home h1 {
  padding:15px 0px;
  font-size: 3.0em;
  line-height: 1;
}


@media (max-width: 1200px) {
  .full-width-image-with-content .custom-content-home {
  }
  .full-width-image-with-content {
    overflow:  hidden;
  }
   .full-width-image-with-content .custom-content-home h1{
    overflow:  hidden;
    font-size: 3rem; !important
  }
  }
  
  @media (max-width: 767px) {
      
  .full-width-image-with-content .custom-content-home {
   position: relative;
    min-width: 100%;
    top: auto;
    padding: 20px;
    background-color: rgba(240,240,240,0.9)!important;
    left: 0;
  }
   .full-width-image-with-content .custom-content-home h1 {
      font-size: 3rem !important;
  }
   .full-width-image-with-content img {
    width: auto;
    max-width: 100% !important;
    object-fit: cover;
  }
  
  }

@media (min-width:767px) and (max-width:1024px)
  {
     .full-width-image-with-content .custom-content-home h1 {      
  }
}
  
  @media (max-width: 479px) {
   .full-width-image-with-content .custom-content-home h1 {
      font-size: 36px;
  }
  
}


/*========================================================
        Get in touch module Styling
========================================================*/

.getintouch-image, .getintouch-image-option-b {
  position: relative;
  height: 350px;
  display: block;
  width: 100%;
  max-width: 450px;
  float: right;
  text-decoration: none;
  margin: 0 auto 1.5em 0; /*delete if messes with other pages */
}

.getintouch-image a, .getintouch-image-option-b a {
  text-decoration: none;
}

.getintouch-image h2, .getintouch-image-option-b h2 {
  line-height: 1.1;
}

.getintouch-image img, .getintouch-image-option-b img {
   height: 350px;
   width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;
  object-fit: cover;
  z-index: 0 !important;
}

.getintouch-image .getintouch-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
}

.getintouch-image .getintouch-text, .getintouch-image-option-b .getintouch-text {
  padding: 50px 0;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
  width: 70%;
}
/* Partial overlay css based on getintouch image overlay -NEW*/

.getintouch-image .getintouch-part-overlay-text-flex, .getintouch-image-option-b .getintouch-part-overlay-text-flex {
  display: flex;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.7);
  position: absolute;
  bottom: 0;
  width: 100%;
  justify-content: center;
  padding: 1em 0.25em 0.25em;
}

/* Partial overlay css - END*/
.description-box {
  padding: 1em;
  background-color: #fff;
  border: 1px solid #000;
  margin-top: -5px;
}

@media (max-width: 767px){

.header-container.container-fluid .row-fluid .page-center {
padding: 0;
}
  
.getintouch-image .getintouch-text {
   padding: 50px 15px;
   transform: translate(-50%, -50%);
}
  
  .getintouch-image, .getintouch-image-option-b  {
  margin: 0 auto 1em 0;
  }
}

@media (min-width:500px) and (max-width:767px)
  {
      .getintouch-image, .getintouch-image-option-b {
  max-width:100%;
  }
}

@media (min-width: 767px) and (max-width:1100px){

.header-container.container-fluid .row-fluid .page-center {
  padding: 0;
}
  
.getintouch-image, .getintouch-image-option-b  {
  margin: 0 auto 1em 0;
  height: 350px;
    }
  
.getintouch-image img, .getintouch-image-option-b img  {
  height: 350px !important;
  
  }
  
.getintouch-image .getintouch-text {
  padding: 50px 15px;
  transform: translate(-50%, -50%);
}
}

/*========================================================
        Rectangular-image module Styling
========================================================*/
.rectangular-image {
background-repeat: no-repeat;
background-size: cover;
background-position: 50% 50%;
position: relative;
height: 100%;
display: inline-block;
width: 100%;
}

.rectangular-image img{
   height: 350px;
   width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;
  object-fit: cover;
}

.rectangular-image .rectangular-image-overlay {

position: absolute;
top: 0;
left: 0;
width: 100%;
height: 100%;
background-color: rgba(0, 0, 0, 0.5);

}

.rectangular-image .rectangular-image-text {

padding: 50px 0;
text-align: center;
position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
z-index: 1;
width: 70%;
}

@media (max-width: 767px){

.header-container.container-fluid .row-fluid .page-center {

padding: 0;

}

.rectangular-image .rectangular-image-text {

padding: 50px 15px;

}
}

@media (max-width: 300px)
  {
 .rectangular-image
    {
      
      width: 100%;
    }
     .rectangular-image img
    {
      height:450px;
    }
    
}

@media (min-width: 767px) and (max-width:1100px){

.header-container.container-fluid .row-fluid .page-center {
padding: 0;
}
  
.rectangular-image .rectangular-image-text {
padding: 50px 15px;
    transform: translate(-50%, -50%);
}
  
  .rectangular-image img{
  height: 350px;
  }
}


/*========================================================
        Rectangular-solid-color module Styling
========================================================*/

.rectangular-solid-color {
  background-color: ;
background-repeat: no-repeat;
background-size: cover;
background-position: 50% 50%;
position: relative;
height: 100%;
display: inline-block;
width: 100%; 
}


 .rectangular-solid-color .rectangular-solid-background {
   height: 350px;
   width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
   width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
  width: fill-available;
  object-fit: cover;
} 


.rectangular-solid-color .rectangular-image-text {

padding: 50px 0;
text-align: center;
position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
z-index: 1;
width: 70%;
}

@media (max-width: 767px){

.header-container.container-fluid .row-fluid .page-center {
    padding: 0;}

.rectangular-solid-color .rectangular-image-text {
    padding: 50px 15px;}
}

@media (max-width: 300px) {
 .rectangular-solid-color{ 
      width: 100%;}
 .rectangular-solid-color .rectangular-solid-background{
      height:450px;}
}

@media (min-width: 767px) and (max-width:1100px){

.header-container.container-fluid .row-fluid .page-center {
padding: 0;
}
  
.rectangular-solid-color .rectangular-image-text {
padding: 50px 15px;
    transform: translate(-50%, -50%);
}
  
  .rectangular-solid-color .rectangular-solid-background {
  height: 350px;
  }
}

/*========================================================
        Guarantee module Styling
========================================================*/
.fixed-height600
{ min-height:600px;
}
.fixed-height750
{ min-height:600px;
}


/*========================================================
        Article module Styling
========================================================*/

.article-container {
  width: 100%;
}

.article-container h5 {
  text-transform: uppercase;
  color: #F46419;
}

.article-image-container {
  position: relative;
  margin: 0px auto;
  height: 450px;
  width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;    
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;  
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
  filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='.myBackground.jpg', sizingMethod='scale');
  -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='myBackground.jpg', sizingMethod='scale')";
}

.article-image-container img{
  object-fit: cover;
  width: 100%;
  max-height: 450px;
}
.white-text-block {
  position: absolute !important;
  bottom: 5%;
  right: 5%;
  left: 5%;
  background-color: #fff;
  padding: 1em;
  width:60%;
  text-align: left;
}

.white-text-block h5 {
  text-transform: uppercase;
  color: #F46419;
}

.article-flex-row {
  display: inline-flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  align-content: stretch;
  flex-wrap: wrap;
  text-align: center;
}

/*========================================================
        Accommodation module Styling
========================================================*/

.accommodation-image-container {
  position: relative;
}

.accommodation-image-container img{
  object-fit: cover;
  width: auto;
  max-height: 250px;
}

.display-no {
  display: none !important;
}

/*========================================================
        Featured event module Styling
========================================================*/

.featureevent-image-container {
  position: relative;
  margin: 0px auto;
  height: auto;
  width: 100%;
}

.featureevent-image-container img{
  object-fit: cover;
  width: auto;
  height: auto;
}

/*========================================================
        Resort style module Styling
========================================================*/

.resort-icon-container {
  background-color: #fff6e9;
  padding: 1.5em;
  min-height: 300px;
} 

.resort-icon {
  text-align: center;
  margin-bottom: 1em;
  max-height: 100px;
  width: auto;
} 

.resort-style ul, ol{
  list-style-type: none;
  margin-left: -25px;
  column-count: 2;
}

.resort-style li{
  line-height: 1.1;
  text-align: center;
}

/*=====================================================  
        Village Enquire Form Container Styling
=======================================================*/    

.form-container {
  border: 1px solid #E7DDD1;
  padding: 3em;
}

.form-container a{
  text-decoration: none !important;
}

@media (max-width: 767px) {
  .form-container {
  border: 0px;
  padding: 0.5em;
}
}

/*========================================  
        Small Square Styling
========================================*/        

.small-square-container{   
    height: 250px;
    width: 250px;
    position: relative;
    padding:2em;
}

.small-square-image {
background-repeat: no-repeat;
background-size: contain;
background-position: 50% 50%;
position: absolute;
width: 100%;
height:100%;
display: block;
}

.small-square-image .small-square-overlay {
position: absolute;
top: 0;
left: 0;
width: 100%;
height: 100%;
background-color: rgba(0, 0, 0, 0.5);
}

.small-square-image .small-square-text {
padding: 50px 0;
text-align: center;
position: absolute;
top: 50%;
left: 50%;
transform: translate(-50%, -50%);
z-index: 1;
width: 70%;
font-family: sans-serif;
}

@media (max-width: 991px){

.small-square-image .small-square-text {
padding: 50px 20px;
}
  .small-square-container{
       height:150px; width:150px;
}
}

@media (max-width: 767px){
.small-square-container .header-container.container-fluid .row-fluid .page-center {
padding: 0;
}

.small-square-image .small-square-text {
padding: 50px 15px;
}
}

/*=====================================================  
       Sticky nav Styling
=======================================================*/    



/*========================================================
        Responsive Styling
========================================================*/

@media (min-width:769px) and (max-width:1024px){

.row-fluid [class*="span"].wrapper{
    padding-left:40px;
    padding-right:40px;
}

.pth-header .row-fluid [class*="span"].wrapper{
    padding-left:15px;
    padding-right:15px;
}


h1{
    font-size:40px;
}

h2{
    font-size: 30px;
}

h3{
    font-size: 20px;
}

h4{
    font-size: 18px;
}

h5{
    font-size: 16px;
}

h6{
    font-size: 16px;
}

.section-padding {
    padding: 1em 0 !important;
}

body .container-fluid .row-fluid .section-heading.align-center {
    margin-bottom: 30px;
}

p{
    font-size: 15px;
}

.body-container-wrapper li {
    font-size: 15px;
}

.section-heading p {
    font-size: 15px;
}

.cta-margin {
    margin-top: 30px;
}

}


@media (max-width:768px){


.row-fluid [class*="span"].wrapper{
    padding-left:20px;
    padding-right:20px;
}

h1{
    font-size:32px;
}
h2{
    font-size:28px;
}
h3{
    font-size: 24px;
}
h4{
    font-size: 18px;
}
h5{
    font-size: 16px;
}
h6{
    font-size: 16px;
}
  div#blockquote-icon p::before
  {
   position:inherit; 
  }

.section-padding {
    padding: 1em 0 !important;
}
.align-right {
    text-align: center;
}
.align-left {
    text-align: center;    
}

body .container-fluid .row-fluid .section-heading.align-center{
    margin-bottom: 30px;
}

p {
    font-size: 15px;
    line-height: 24px;
}
.body-container-wrapper li {
    font-size: 15px;
    line-height: 24px;
}

.primary-banner {
    padding: 120px 0 40px;
}

.section-heading p {
    font-size: 15px;
}

.heading-underline {
    margin-bottom: 20px;
    padding-bottom: 2px;
}

.cta-margin {
    margin-top: 20px;
}

.body-container-wrapper {
    padding-top:0px;   
}
}

@media (max-width:640px){

}


/*========================================  
        Header Styling
========================================*/          

@media (min-width:768px){

.pth-header .header-logo{
    padding: 0.5rem 0;
}

.pth-header .header-logo img{
    width:100%;
    max-width:200px;
}

}

.pth-top-bar{
    padding:10px 0;
    background:#F8F7F6;
}

.pth-top-bar .widget-type-cta{
    margin:0;
    text-align:right;
}

.pth-top-bar a.cta_button{
    padding:9px 20px;
    font-size:12px;
}

.pth-top-bar .phone-number-container a span{
    font-weight:500;
} 

.pth-top-bar .phone-number-container .fa{
    color:#F46419;
    font-size: 20px;
    margin-right: 4px;    
}
@media (max-width:767px){
    
.pth-top-bar .phone-number-container .fa {
    font-size: 14px;
    margin-right: 2px;
}

.pth-top-bar a.cta_button {
    padding: 3px 6px;
    font-size: 12px;
}

.pth-top-bar .phone-number-container {
    font-size: 12px;
}

body .row-fluid .pth-top-bar .phone-number-container{
    width: 48%;
    float: left;
    margin-top: 8px;
}

body .row-fluid .pth-top-bar .top-cta-container{
    width:40%;
    float:right;
}
}

/*************************************
            HEADER STYLING
*************************************/            

body .row-fluid .pth-header{
    background-color: #ffffff;
   // border-bottom: 1px solid #e1e2e3;
    position:relative;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 11;
    transition: all .35s ease-in-out;
    top: 0;
   // box-shadow: 0px 0px 3px 0px rgba(0, 0, 0, 0.50);
}

body .row-fluid .pth-header.fixed-nav {
    //top: -86px;
}

.pth-header .logo{
    padding:10px 0;
}

.pth-header .head-cta{
    margin-top:0;
}

.pth-header .head-cta a.cta_button{
    padding: 12px 30px;    
    border:2px solid #F46419;
}

.pth-header .top-nav-container{
    margin-top:50px;
}

.pth-header .contact-info span{
    font-size:12px;
}

.pth-header .contact-info span .fa{
    margin-right: 7px;
    width: 12px;
    text-align: center;
    font-size: 16px;
    position: relative;
    top: 1px;
}
@media(max-width:1200px) and (min-width:768px){
    
.hs-menu-wrapper.hs-menu-flow-horizontal > ul>li.hs-item-has-children:last-child>ul.hs-menu-children-wrapper{
    left:-125px;        
}
}
@media (min-width:768px){
    
body .row-fluid .pth-header{
    height:;
}    

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal > ul>li>a{
    font-size: 14px;
    padding:35px 12px;
    line-height:1;
    font-weight:400;
}

.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper{
    border-top:3px solid #F46419;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-menu-depth-1 {
    line-height: 0;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-menu-depth-1.hs-item-has-children>a{
    position:relative;
    margin-right:12px;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-menu-depth-1.hs-item-has-children>a:focus{
    font-weight: bold;
    color:#F46419 !important;
}
 
/*
 * HIDDEN FROM NAV
 * 
 * .pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-item-has-children>a:before{
    position: absolute;
    content: '\f107';
    font-family:FontAwesome;
    top: -2px;
    bottom: 0;
    right: 12px;
    font-size: 14px;
    color:#ffffff;
    display: flex;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    justify-content: center;
    -webkit-justify-content: center;
    justify-content: center;
}
  */
  
.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-menu-depth-1.hs-item-has-children>a:before{
    right:-5px;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-menu-depth-1:hover a,
.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-menu-depth-1.active a,
.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-item-has-children:hover>a:before{
    font-weight: bold;
}


.pth-header .hs-menu-wrapper {
    line-height: 0;
    text-align: center;
    float:right;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li a{
    font-size: 14px;
    padding: 12px 24px 12px 15px;
    line-height: 1;
    color: #191919 ;
    text-align: left;
    background: #fff ;
    border-top: 0;
    font-weight: 400;
    transition:all .2s ease-in-out;
    min-width: 140px;
    -webkit-transition:all .2s ease-in-out;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li a:before{
    color:#191919 ;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li:hover a:before{
    color:#F46419;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li a:hover{
    color:#F46419 !important;
    text-indent:2px;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul li.hs-item-has-children ul.hs-menu-children-wrapper li.hs-item-has-children ul.hs-menu-children-wrapper {
    left: 100%;
    top: 0;
}
}

@media (min-width:768px) and (max-width:1024px){

.pth-header .head-cta a.cta_button
  {
    padding: 10px 18px;
    font-size: 14px;
}

.pth-header .hs-menu-wrapper.hs-menu-flow-horizontal>ul>li>a {
    //padding: 22px 10px 22px;
    font-size:12px;
}

.pth-header .top-nav-container {
    margin-top: 34px;
}

.pth-header {
    padding:0;
}
}

@media (max-width:767px){

}

/*==================================================
            Two Col Adjust 
==================================================*/

body .container-fluid .row-fluid .two-col-adjust>span {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    flex-wrap: wrap;
    -wevkit-flex-wrap: wrap;
    flex-direction: row;
    -webkit-flex-direction: row;
    margin-right: -2.5%;
}

body .row-fluid .two-col-adjust span>.hs_cos_wrapper {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-basis: 47.4%;
    -webkit-flex-basis: 47.4%;
    float: left;
    margin-right: 2.43%;
    margin-top: 25px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position:relative;
}

/* Make border boxed*/

body .container-fluid .row-fluid .two-col-adjust.have-box-border span>.hs_cos_wrapper{
    background-color: #fff;
    box-shadow: 0 1px 1px 0 #e6ebee;
    border: solid 1px rgba(129,154,179,0.14);
    position: relative;
    -webkit-transition: all .4s ease;
    -moz-transition: all .4s ease;
    -ms-transition: all .4s ease;
    -o-transition: all .4s ease;
    transition: all .4s ease;
    padding:20px;
    position:relative;
} 


@media (max-width:767px){
 
body .container-fluid .row-fluid .two-col-adjust>span {
    display: block;
    margin-right: 0;
}

body .row-fluid .two-col-adjust span>.hs_cos_wrapper {
    display: block;
    margin: 0;
    margin-top: 20px;
    max-width: 379px;
    width: 100%;
    margin: 30px auto 0;
    float: none;
}   
}

/*==================================================
            Three Col Adjust 
==================================================*/

body .container-fluid .row-fluid .three-col-adjust>span {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    flex-wrap: wrap;
    -wevkit-flex-wrap: wrap;
    flex-direction: row;
    -webkit-flex-direction: row;
    margin-right: -2.5%;
}

body .row-fluid .three-col-adjust span>.hs_cos_wrapper {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-basis: 30.2%;
    -webkit-flex-basis: 30.2%;
    float: left;
    margin-right: 2.43%;
    margin-top: 25px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position:relative;
}

/* Make border boxed*/

body .container-fluid .row-fluid .three-col-adjust.have-box-border span>.hs_cos_wrapper{
    background-color: #fff;
    box-sizing: border-box;
    box-shadow: 0 1px 1px 0 #e6ebee;
    border: solid 1px rgba(129,154,179,0.14);
    position: relative;
    -webkit-transition: all .4s ease;
    -moz-transition: all .4s ease;
    -ms-transition: all .4s ease;
    -o-transition: all .4s ease;
    transition: all .4s ease;
    padding:20px 0;
    position:relative;
} 

body .container-fluid .row-fluid .three-col-adjust.have-box-border span>.hs_cos_wrapper>div{
    padding:0 20px;
}
.redirect-page{
    position:absolute;
    top:0px;
    bottom:0px;
    left:0px;
    right:0px;
    width:100%;
    height:100%;
    display:block;
    cursor:pointer;
    -webkit-transition: all .4s ease;
    -moz-transition: all .4s ease;
    -ms-transition: all .4s ease;
    -o-transition: all .4s ease;
    transition: all .4s ease;
}

body .container-fluid .row-fluid .three-col-adjust.have-box-border span>.hs_cos_wrapper:hover .redirect-page{
    border:1px solid #F46419;
}


@media (max-width:767px){
 
 
body .container-fluid .row-fluid .three-col-adjust>span {
    display: block;
    margin-right: 0;
}

body .row-fluid .three-col-adjust span>.hs_cos_wrapper {
    display: block;
    margin: 0;
    margin-top: 20px;
    max-width: 379px;
    width: 100%;
    margin: 30px auto 0;
    float: none;
}   
}
/*==================================================
            Four Col Adjust 
==================================================*/

body .container-fluid .row-fluid .four-col-adjust>span {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    flex-wrap: wrap;
    -wevkit-flex-wrap: wrap;
    flex-direction: row;
    -webkit-flex-direction: row;
    margin-right: -2.565%;
}

body .row-fluid .four-col-adjust span>.hs_cos_wrapper {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-basis: 22.5%;
    -webkit-flex-basis: 22.5%;
    float: left;
    margin-right: 2.5%;
    margin-top: 20px;
    box-sizing:border-box;
    position:relative;
}
@media (max-width:767px){
 
body .container-fluid .row-fluid .four-col-adjust>span {
    display: block;
    margin-right: 0;
}

body .row-fluid .four-col-adjust span>.hs_cos_wrapper {
    display: block;
    flex-basis: 100%;
    width:100%;
    -webkit-flex-basis: 100%;
    margin:0;
    margin-top: 20px;
}   
}

/*=========================================================
                    FORM STYLE
=========================================================*/                

::-webkit-input-placeholder {
   color: #989898;
}

:-moz-placeholder {
   color: #989898;
}

::-moz-placeholder {  
   color: #989898;
}

:-ms-input-placeholder {  
   color: #989898;
}

select::-ms-expand {
    display: none;
}

.row-fluid form li{
    margin:0;
}

.row-fluid  form li:before{
    display:none;
}

input[type="password"] {
  height: 54px !important;
}

.row-fluid  form input,
.row-fluid  form select,
.row-fluid  form textarea{
    height: 54px;
    font-family: 'Work Sans', sans-serif; ;
    border: 0;
    font-size: 14px;
    line-height: 20px;
    letter-spacing: 0.25px;
    padding: 0;
    margin-bottom: 8px;
    border-radius: 0;
    background: #f5f5f5;
    padding:10px 20px;
    color: #191919 ;
    font-weight:400;
    background:#fff;
    border:1px solid #dedede;
    box-sizing: border-box;
    -webkit-appearance:none;
    -moz-appearance:none;
    appearance:none;
}


.row-fluid  form input:focus,
.row-fluid  form select:focus,
.row-fluid  form textarea:focus{
    outline:0;
    border:1px solid #F46419;
}

.row-fluid  form textarea{
    height:150px;    
}

.row-fluid  form .hs_file_upload input,
.row-fluid  form .hs-form-checkbox input,
.row-fluid  form .hs-form-radio input,
.row-fluid  form .hs-form-booleancheckbox input{
    height:auto;
    padding:0;
    border:0;
}

.row-fluid  form .hs-form-checkbox input,
.row-fluid  form .hs-form-radio input,
.row-fluid  form .hs-form-booleancheckbox input{
    height:auto;
    padding:0;
    border:0;
    margin-bottom:0;
}

.row-fluid form .field>label{
    font-size: 14px;
    line-height: 1;
    letter-spacing: 0.25px;
    font-weight: 300;
    display:block;
    margin-bottom:6px;
    margin-top:14px;
}

.row-fluid form .hs_submit .actions{
    text-align:center;
}

.row-fluid form .hs-button.primary{
    height: auto;
    margin-top: 24px;
}

form .hs-error-msgs{
    margin:0;
    padding:0;
}

form .hs-error-msgs li{
    line-height: 1;
    color: red;
    font-weight: 500;
    font-size: 11px;
    position: relative;
    top: -4px;
    letter-spacing: 0.3px;
}

.row-fluid .hs-form fieldset.form-columns-1 .hs-input {
    width: 100%;
}

body .row-fluid .hs-form fieldset {
    max-width: 100%;
}

form .hs-form-required{
    display:none;
}

.body-container-wrapper form ul,
.body-container-wrapper form li{
    margin:0;
    padding:0;
}

.body-container-wrapper form ul,
form ul{
    list-style:none;
}

.body-container-wrapper form li:before{
    display:none;
}

body form .hs-form-checkbox:last-child,
body form .hs-form-radio:last-child,
body form .hs-form-booleancheckbox{
    margin-bottom:20px;
}

body .row-fluid .hs-form fieldset.form-columns-3 .hs-form-field {
    width: 32%;
    margin-right:2%;
}

body .row-fluid .hs-form fieldset.form-columns-2 .hs-form-field {
    width: 49%;
    margin-right:2%;
}

body .row-fluid .hs-form fieldset .hs-form-field:last-child{
    margin-right:0;
}

body .row-fluid .hs-form fieldset.form-columns-2 .input,
body .row-fluid .hs-form fieldset.form-columns-3 .input {
    margin-right:0px;
}

body .container-fluid .row-fluid .hs-form fieldset .field:last-child .input{
    margin-right:0;
}

body .row-fluid select{
    background: #fff url(//2558848.fs1.hubspotusercontent-na1.net/hubfs/2558848/Product-3-Pack/Select%20Arrow%20Small.png) no-repeat;
    background-position: center right;
    -webkit-appearance: none;
}

/*=======================================================
            CHECKBOX AND RADIO BUTTONS STYLING
=======================================================*/            

.row-fluid .hs-form .field .input input[type="checkbox"],
.row-fluid .hs-form .field .input  input[type="radio"]{
    width:auto;
}

.row-fluid .hs-form .field .input input[type="checkbox"],
.row-fluid .hs-form .field .input  input[type="radio"]{
    padding: 0;
    border-radius: 4px;
    background: #fff;
    margin-left: 0;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    color: #cfd3d7;
    -webkit-appearance: none;
    border: 2px solid #ddd;
    margin-right: 8px;
    top: 2px;
    width: 20px;
    height: 20px;
}

.row-fluid .hs-form .field .input input[type="checkbox"]:checked:after {
    content: '';
    position: absolute;
    width: 9px;
    height: 5px;
    top: 3px;
    left: 2px;
    border: 3px solid #F46419;
    border-top: 0;
    border-right: 0;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
}


.row-fluid .hs-form .field .input  input[type="radio"]{
    border-radius: 50%;
}

.row-fluid .hs-form .field .input  input[type="radio"]:checked:after {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    top: 3px;
    background: #F46419;
    left: 3px;
    border-top: 0;
    border-right: 0;
    border-radius: 50%;
}


@media (max-width:480px){


body form.hs-form .form-columns-2 .hs-form-field .hs-input,body form.hs-form .form-columns-3 .hs-form-field .hs-input{
    width:100%;
}

body .row-fluid .hs-form fieldset .input{
    margin:0;
}

body .row-fluid .hs-form fieldset.form-columns-3 .hs-form-field {
    width: 100%;
    margin-right: 0;
}

body .row-fluid .hs-form fieldset.form-columns-2 .hs-form-field {
    width: 100%;
    margin-right: 0;
}

.row-fluid form .hs_submit .actions{
    text-align:center;
}

}


/*============================ Talk To Us Section ==============================*/


.talk-to-us-99,.talk-to-us-main{
    padding:130px 0;   
}
    
body .container-fluid .row-fluid .talk-to-us-99 .section-heading{
    max-width:1060px;
    padding:0;
}

body .row-fluid .talk-to-us-99 .icon-img{
    width: 100%;
    max-width: 110px;
    margin: 20px auto 30px;
    float: none;
    border: 2px solid #fff;
    height: 110px;
    padding: 23px;
    border-radius: 50%;
}

.talk-to-us-99 h3{
    color#191919 ;
    font-weight: 600;
    font-size: 30px;
}

.talk-to-us-99 .button a{
    color: #fff;
    border: 2px solid;
    padding: 14px 26px;
    display: inline-block;
    margin-top: 10px;
    font-weight: 600;
    letter-spacing: 0.25px;
    width: 100%;
    max-width: 210px;
}

.talk-to-us-99 .button a img {
    width: 25px;
    padding-right: 10px;
}


.talk-to-us-99 .button a .fa{
    font-size: 20px;
    position: relative;
    margin-right: 14px;
}

.talk-to-us-99 .divider{
    margin:50px 0 30px;
}

.contact-assets{
    padding: 30px 0px;
}

.phone-nos{
    position:relative;
    padding-left:50px;
}

.email-address{
    position:relative;
    padding-left:50px;
}

.phone-nos a,.email-address a{
    font-size: 16px;
    font-weight: 600;
}

.phone-nos:before,
.email-address:before{
    content: "\f095";
    font-family: FontAwesome;
    color: #F46419;
    width: 40px;
    height: 40px;
    border-radius: 100%;
    display: block;
    background: #fff;
    line-height: 45px;
    text-align: center;
    font-size: 20px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.55);
    position: absolute;
    left: 0px;
    top: -10px;
}

.row-fluid .email-address:before{
    content:"\f0e0";
    line-height: 40px;
}

.contact-assets{
    
}

.form-wrapped {
    max-width: 450px;
    position:relative;
}

.form-wrapped:before{
    content: "Or";
    font-weight: 600;
}

body .row-fluid .form-wrapped form .hs_submit .actions{
    text-align:left;
}
/*
body .row-fluid .form-wrapped form .hs_submit .hs-button {
    background-image:url('//2432204.fs1.hubspotusercontent-na1.net/hubfs/2432204/99%20Template%20Pack/Background%20Images/send-option.png');
    background-repeat: no-repeat;
    background-position: right center;
    background-size: 50px;
    padding-right: 65px;
}*/

.talk-to-us-main.overlay:before{
    background: rgba(255, 255, 255, 0.9);
}


@media (max-width:1024px){
 
.talk-to-us-99 {
    padding: 70px 0;
} 
}



@media (max-width:767px){

body .container-fluid .row-fluid .talk-to-us-99 .section-heading {
    padding: 0;
    margin-bottom: 0;
}

body .row-fluid .talk-to-us-99 .icon-img{
    margin: 20px auto 20px;
}


.talk-to-us-99 {
    padding: 50px 0;
} 

.talk-to-us-main {
    padding: 75px 0;
}

.widget-type-rich_text.phone-nos {
    margin-bottom: 40px;
}

.talk-to-us-main .wrapper>.row-fluid-wrapper>.row-fluid {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
}

.talk-to-us-main .wrapper>.row-fluid-wrapper>.row-fluid .span5{
    order:1;
    margin-bottom:50px;
}


.talk-to-us-main .wrapper>.row-fluid-wrapper>.row-fluid .span7{
    order:2;
}

}    

/* ==========================================================================
   Mega Menu Styling
   ========================================================================== */

body {
  font-family: 'Work Sans', sans-serif;
}
* {
  box-sizing: border-box;
}
a {
  color: #333;
}

.menu-right {
  float: right !important;
}

.menu {
    width: 100%;
}
.menu-container {
  margin: 0 auto;
  background: #fff;
  float: right;
}
.menu a.logo {
    display: inline-block;
    padding: 1.5em 3em;
    width: 19%;
    float: left;
}
.menu img {
    max-width: 100%;
    position: absolute;
    z-index: 999;
}
.menu-mobile {
  display: none;
  padding: 20px;
}
.menu-mobile:after {
  content: "|||";
  font-size: 2.5rem;
  color: #F2A628;
  font-weight: 600;
  padding: 0;
  float: right;
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-25%);
          transform: translateY(-25%);
  -webkit-transform: rotate(-90deg); /* Safari */
  -moz-transform: rotate(-90deg); /* Firefox */
  -ms-transform: rotate(-90deg); /* IE */
  -o-transform: rotate(-90deg); /* Opera */
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3); /* Internet Explorer */
  overflow: scroll !important;
  max-height: 83vh;  
}

.menu-dropdown-icon:before {
  content: "+";
  display: none;
  cursor: pointer;
  float: right;
  padding: 1.5em 2em;
  color: #333;
  position: absolute !important;
  right: 5% !important;  
}
.menu > ul {
  margin: 0 auto;
  width: 100%;
  list-style: none;
  padding: 0;
  position: ;
  /* IF .menu position=relative -> ul = container width, ELSE ul = 100% width */
  box-sizing: border-box;
      clear: right;
}
.menu > ul:before,
.menu > ul:after {
  content: "";
  display: table;
}
.menu > ul:after {
  clear: both;
}
.menu > ul > li {
  float: left;
  background: #fff;
  padding: 0;
  margin: 0;
}
.menu > ul > li a {
  text-decoration: none;
  padding: 1.5em;
  display: block;
}
.menu > ul > li:hover {
  background: #F7CB7B;
  font-weight: 600;
}
.menu > ul > li > ul {
  display: none;
  width: 100%;
  background: #fff;
  padding: 20px;
  position: absolute;
  z-index: 99;
  left: 0;
  margin: 0;
  list-style: none;
  box-sizing: border-box;
}
.menu > ul > li > ul:before,
.menu > ul > li > ul:after {
  content: "";
  display: table;
}
.menu > ul > li > ul:after {
  clear: both;
}
.menu > ul > li > ul > li {
  margin: 0;
  padding-bottom: 0;
  list-style: none;
  width: 23%;
  background: none;
  float: left;
  padding: 1.5em;
}
.menu > ul > li > ul > li:first-child {
  width: 50%;
  margin-right: 3em;
}
.menu > ul > li > ul > li a {
  color: #777;
  padding: 0;
  width: 100%;
  display: block;
  border-bottom: 1px solid #ccc;
}
.menu > ul > li > ul > li a:hover{
	color: #F2A628;
}
.menu > ul > li > ul > li > ul {
  display: block;
  padding: 0;
  margin: 10px 0 0;
  list-style: none;
  box-sizing: border-box;
}
.menu > ul > li > ul > li > ul:before,
.menu > ul > li > ul > li > ul:after {
  content: "";
  display: table;
}
.menu > ul > li > ul > li > ul:after {
  clear: both;
}
.menu > ul > li > ul > li > ul > li {
  float: left;
  width: 100%;
  padding: 10px 0;
  margin: 0;
  font-size: 1em;
}
.menu > ul > li > ul > li > ul > li a {
  border: 0;    
  font-size: 1em;
  font-weight: 400;
}
.menu > ul > li > ul.normal-sub {
  width: 300px;
  left: auto;
  padding: 10px 20px;
}
.menu > ul > li > ul.normal-sub > li {
  width: 100%;
}
.menu > ul > li > ul.normal-sub > li a {
  border: 0;
  padding: 1em 0;
}

.bold a{
  font-weight: 600 !important;
}

.bold > ul > li > ul > li > ul > li a {
  font-weight: 600 !important;
  color: #000 !important;
}

@media only screen and (max-width: 1260px) and (min-width: 1024px) {
.menu > ul > li > ul > li:first-child {
  margin-right: 0;
}
}
/* ââââââââââââââââââââââââââââââââââââââââââââââââââ
Mobile style's
ââââââââââââââââââââââââââââââââââââââââââââââââââ */
@media only screen and (max-width: 1024px) {
  .menu-container {
    width: 100%;
  }
  .menu-container .menu{
	display:inline-block;
   }
  .menu-mobile {
    display: block;    
    float: right;    
    padding: 12px 40px 0;
    position: absolute;
    top: 0;
    right: 0;
    margin-top: 3em;
  }
  .menu-dropdown-icon:before {
    display: block;
  }
  .menu > ul {
    display: none;
    width:100%;
  }
  .menu > ul > li {
    width: 100%;
    float: none;
    display: block;
    background-color: #f0f0f0;
    border-top: 1px solid;
  }
  .menu > ul > li a {
    padding: 1.5em;
    width: 100%;
    display: block;
  }
  .menu > ul > li > ul {
    position: relative;    
    padding: 0;
  }
  .menu > ul > li > ul.normal-sub {
    width: 100%;
  }
  .menu > ul > li > ul > li {
    float: none;
    width: 100%;
  }
  .menu > ul > li > ul > li:first-child {
    margin: 0;
  }
  .menu > ul > li > ul > li > ul {
    position: relative;
  }
  .menu > ul > li > ul > li > ul > li {
    float: none;
  }
  .menu > ul > li > ul > li > ul > li a {
    font-weight: 400;
    overflow-wrap: break-word;    
  }
  .menu .show-on-mobile {
    display: block;
    font-size: 1.25em;
    margin-top: 1.5em;
  }
  .hide-call-to-action {
    display: none !important;
  }
  .hide-call-to-action a img {
    display: none !important;
  }  
}


/* ==========================================================================
   Custom Menu Primary
   ========================================================================== */


 /* Set ul background color */
 /* Set li background Color */
 /* Set link Color */
 /* Set link Hover Color */

/* Parent List */
.custom-menu-primary .hs-menu-wrapper > ul{ 
  background:#fff;
}
.custom-menu-primary .hs-menu-wrapper > ul > li{
  background:#fff;
}
.custom-menu-primary .hs-menu-wrapper > ul > li > a{
  color:#191919;
}
.custom-menu-primary .hs-menu-wrapper > ul > li > a:hover{
  background-color:#F46419 ;
  font-weight: 500;
}

/* Child List */
.custom-menu-primary .hs-menu-wrapper > ul ul{}
.custom-menu-primary .hs-menu-wrapper > ul ul li{
  background:#fff;
}
.custom-menu-primary .hs-menu-wrapper > ul ul li a{
  color:#191919;
}
.custom-menu-primary .hs-menu-wrapper > ul ul li a:hover{
  color:#F2A628;
}

/* Override max width on menu links */
.custom-menu-primary .hs-menu-wrapper > ul li a, 
.hs-menu-wrapper.hs-menu-flow-horizontal > ul li.hs-item-has-children ul.hs-menu-children-wrapper li a {
  overflow: visible !important;
  max-width: none !important;
  width: auto !important;
}

/* Fix menu disappearing on desktop after toggling mobile menu */
@media screen and (min-width:768px) {
      .custom-menu-primary .hs-menu-wrapper { 
          display:block !important;
      }
  }




/* ==========================================================================
   Mobile Menu - Hubspot Standard Toggle Menu
   ========================================================================== */


/**
 * Special Note
 *
 * When the menu is open, a class of .mobile-open is applied to the body. You can 
 * use this for custom styling on any element when the menu is in the open position.                     
 */

.mobile-trigger, .child-trigger{
    display: none; /* Hide button on Desktop */
}

@media (max-width: 767px){


  /* Variables
     ========================================================================== */

     /* Set Mobile Menu Background Color */
        /* Set Link Color */
   /* Set Link Hover Color */

  /* 
    * Menu Reset
    *
    * Remove styling from desktop version of custom-menu-primary. Place any 
    * additional CSS you want removed from the mobile menu in this reset 
    */

  .custom-menu-primary,
  .custom-menu-primary .hs-menu-wrapper > ul,
  .custom-menu-primary .hs-menu-wrapper > ul li,
  .custom-menu-primary .hs-menu-wrapper > ul li a{
    display: block;
    float: none;
    position: static;
    top: auto;
    right: auto;
    left: auto;
    bottom: auto;
    padding: 0px;
    margin: 0px;
    background-image: none;
    background-color: transparent;
    border: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    -webkit-box-shadow: none;
    -moz-box-shadow:    none;
    box-shadow:         none; 
    max-width: none;
    width: 100%;
    height: auto;
    line-height: 1;  
    font-weight: normal;
    text-decoration: none;
    text-indent: 0px;
    text-align: left;
    color:#191919;
  }


  /* Toggle Button
     ========================================================================== */

  .mobile-trigger{
    display: inline-block !important; /* Show button on mobile */
    cursor: pointer; /* Mouse pointer type on hover */
    position: absolute; /*******************************************/
    top: 0px;          /* Position Button at right of screen  */
    right: 10px;        /*******************************************/
    width: auto; /* Button width */
    height: auto; /* Button height */      
    padding: 7px 10px 8px 10px;
    background: #ffffff; /* Background color */
    font-size: 16px;
    font-weight: normal;
    text-align: left;
    text-transform: uppercase;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    color: #ffffff; 
  }
  .mobile-trigger:hover{
    text-decoration: none; /* Removes link text underline on button */
    color:#000000;
    background-color: #ffffff; 
    border-color: transparent;
  }

  /* Change button when menu is open */
  .mobile-open .mobile-trigger{
    color:#000000;
    background-color: #ffffff; 
    border-color: transparent;
  }


  /* Toggle Button Icon
     ========================================================================== */

  .mobile-trigger i{
    display: inline;
    position: relative;
    top: 10px;
  }
  .mobile-trigger i:before, .mobile-trigger i:after{
    position: absolute;
    content: '';
  }
  .mobile-trigger i, .mobile-trigger i:before, .mobile-trigger i:after{
    width: 22px; /* Icon line width */
    height: 2px; /* Icon line height */
    -webkit-border-radius: 1px;
    -moz-border-radius: 1px;
    border-radius: 1px;
    background-color: #ffffff; /* Icon color */
    display: inline-block;
  }
  .mobile-trigger i:before{
    top: -6px; /* Position top line */
  }
  .mobile-trigger i:after{
    top: 6px; /* Position bottom line */
  }
  
  .mobile-trigger:hover i, .mobile-trigger:hover i:before, .mobile-trigger:hover i:after,  
  .mobile-open .mobile-trigger i, .mobile-open .mobile-trigger i:before, .mobile-open .mobile-trigger i:after{
      background-color: #000000; /* Icon color */
  }


  /* Child Toggle Button
     ========================================================================== */

  .child-trigger{
    display: block !important; /* Hide button on Desktop */
    cursor: pointer; /* Mouse pointer type on hover */
    position: absolute;
    top: 0px;
    right: 0px;
    width: 55px !important; /* Button width */
    min-width: 55px !important;
    height: 45px !important; /* Button height */  
    padding: 0 !important;
    border-left: 1px dotted rgba(255, 255, 255, .20);
  }
  .child-trigger:hover{
    text-decoration: none;
  }
  .child-trigger i{
    position: relative;
    top: 50%; /* Centers icon inside button */
    margin: 0 auto !important;
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  .child-trigger i:after{
    position: absolute;
    content: '';
  }
  .child-trigger i, .child-trigger i:after{
    width: 10px; /* Icon line width */
    height: 1px; /* Icon line height */
    background-color:#191919; /* Icon color */
    display: block;

  }
  .child-trigger i:after{
    -webkit-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    transform: rotate(-90deg);
  }
  .child-trigger.child-open i:after{
    -webkit-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    transform: rotate(-180deg);
  }
  .child-trigger.child-open i{
  }

    
  /* Menu Styles on Mobile Devices
     ========================================================================== */  
     
   .custom-menu-primary.js-enabled{
       position: relative;
       padding-top: 44px; /* Makes room for button */
       margin: 10px 0 10px 0;
   }

  /* Hide menu on mobile */
  .custom-menu-primary.js-enabled .hs-menu-wrapper,
  .custom-menu-primary.js-enabled .hs-menu-children-wrapper{
    display: none;
  }  

  /* Make child lists appear below parent items */
  .custom-menu-primary ul.hs-menu-children-wrapper{
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    display: none;
  }

  /* Mobile Menu Styles */ 
  .custom-menu-primary.js-enabled .hs-menu-wrapper{
    width: 100%;
    position: relative; /**************************************************************/
    top: 0;             /* Positions the menu to drop from the very top of the screen */
    left: 0;          /**************************************************************/
    padding: 0;
  }
  .custom-menu-primary .hs-menu-wrapper{
    background-color:#ffffff; /* Menu background color set off global menuColorMobile variable */
    width: 100%; /* Full screen width */
  }

   /* Level 1 Menu List Styles */
  .custom-menu-primary .hs-menu-wrapper > ul > li{
    position: relative;
  }
  .custom-menu-primary .hs-menu-wrapper > ul > li a{
    font-size: 22px; /* Font size of top level list items */
    line-height: 45px;
    overflow: visible;
  }

  /* Level 1 and Higher Menu List Styles */
  .custom-menu-primary .hs-menu-wrapper > ul li{
    border-top: 1px dotted rgba(255, 255, 255, .35); /* Adds transparent dark highlights to top of top level list items */
  }
  .custom-menu-primary .hs-menu-wrapper >  ul li a{
    padding: 0 10px;
    color:#191919; /* link color set by global mobile-aColor variable */
  }
  .custom-menu-primary .hs-menu-wrapper > ul li a:hover{
    color:#000000; /* link hover color set by global mobile-aColorHover variable */
  }

  /* Level 2 and Higher Menu List Styles */
  .custom-menu-primary .hs-menu-wrapper > ul ul li{
    background-color: rgba(255, 255, 255, .08);
  }
  .custom-menu-primary .hs-menu-wrapper > ul ul li a{
    text-indent: 10px; /* Indent Child lists */
    font-size: 16px; /* Font size of child lists */
  }

  /* Level 3 and Higher Menu List Styles */
  .custom-menu-primary .hs-menu-wrapper > ul ul ul li a{
    text-indent: 30px; /* Indent Child lists */
  }
  .custom-menu-primary .hs-menu-wrapper > ul ul ul ul li a{
    text-indent: 50px; /* Indent Child lists */
  }
}


/*============================================================
            HEADER Mobile Responsive
===========================================================*/

@media (max-width:767px){

.custom-menu-primary .hs-menu-wrapper{    
    display:none;
}

.mobile-trigger {
    border: 0;
    top: 0;
    right: 14px;    
    background:transparent;
}

.mobile-trigger i, .mobile-trigger i:before, .mobile-trigger i:after {
    width:30px;
    background: #F46419;
    height:4px;
}

.mobile-trigger i:before {
    top: -8px;
}

.mobile-trigger i:after {
    top: 8px;
}

.mobile-trigger {
    top: 10px;
    right: 8px;
}

.mobile-open .mobile-trigger ,
.mobile-trigger {
    background-color: transparent;
    z-index:15;
}

.mobile-trigger:hover {
    background-color: rgba(0, 0, 0, 0);
    border-color: transparent;
}

.mobile-trigger:hover i, .mobile-trigger:hover i:before, 
.mobile-trigger:hover i:after, .mobile-open .mobile-trigger i,
.mobile-open .mobile-trigger i:before, .mobile-open .mobile-trigger i:after {
    background-color: #F46419;
}

.mobile-trigger i{
    transition: all .5s ease-in;
}

.mobile-open .mobile-trigger i{
    background:transparent;
    transition: all .1s ease-in;
}

.mobile-trigger i:before,
.mobile-trigger i:after{
    transition: all .3s ease-in;
}

.mobile-open .mobile-trigger i:before {
    top: 0;
    transform: rotate(45deg);
    background:#F46419;
}

.mobile-open .mobile-trigger i:after{
    top: 0;
    transform: rotate(-45deg);
    background:#F46419;
}

.custom-menu-primary.js-enabled{
    position: absolute;
    padding-top: 0;
    margin: 10px 0 10px 0;
    left: 0;
    right: 0;
    top: 0;
    margin:0;
}

body .header-container-wrapper .pth-header .logo{
    max-width:190px;
    float:left;
    margin-top:0;
    margin-left:20px;
}

.custom-menu-primary .hs-menu-wrapper>ul li {
    border-top: 0px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.24);
}

.child-trigger i, .child-trigger i:after {
    background-color: #000;
}

.custom-menu-primary.js-enabled .hs-menu-wrapper{
    margin: 0;
    z-index: 1;
    box-shadow: 0px 0px 1px #1d1d1d;
    padding: 10px 6px 10px 20px;
    z-index: 11;
    top: 44px;
    box-sizing: border-box;
    margin-top: 2em;
}

body .header-container-wrapper .pth-header ul>li{
    border:0;
    background:transparent;
}

.custom-menu-primary .hs-menu-wrapper>ul>li a {
    font-size: 14px;
    padding:0;
    color:#000;
    font-weight: 500;
    text-align: right;
}

.custom-menu-primary .hs-menu-wrapper>ul>li a:hover{
    color:#000;
}

body .row-fluid .custom-menu-primary .hs-menu-wrapper>ul ul li a {
    font-size: 14px;
    text-indent:0;
}

.child-trigger {
    border-left: 0;
}

body .row-fluid .pth-header .custom-menu-primary .hs-menu-wrapper>ul li.hs-menu-depth-1 .hs-menu-children-wrapper {
    padding: 0 0 0 15px;
    position: relative !important;
    left: 0 !important;
}

.pth-header .custom-menu-primary .hs-menu-wrapper>ul li.hs-menu-depth-1 .hs-menu-children-wrapper:before {
    position: absolute;
    content: '';
    left: 0;
    bottom: 12px;
    top: 10px;
    width: 1px;
    background: rgba(0, 0, 0, 0.33);
}

.pth-header .head-cta a.cta_button{
    display:none;
}

body .row-fluid .pth-header .contact-info {
    display:none;
}

body .row-fluid .pth-header .header-logo {
    width: 160px;
    float: left;   
    text-align:left;
}

.pth-header {
  padding-top: 10px;
  padding-bottom: 45px;
}

}

/****************** END Header Mobile Responsive *******************/

 

/*========================================  
        Footer Styling
========================================*/          

.pth-footer{
    padding:80px 0 40px;
    background:#F46419; /* changed */
}

.pth-footer .social-icons{
  padding-top:25px;
}

.pth-footer .social-icons p {
  color: #fff; /* new */
}

.pth-footer .heading {
  margin-bottom:15px;
  color: #fff; /* new */
}

.pth-footer p {
  color: #fff; /* new */
}

.pth-footer a {
  color: #fff; /* new */
}

.pth-footer .social-icons ul li {
  color: #F46419; /* new */ 
}

.pth-footer .hs-menu-wrapper.hs-menu-flow-vertical > ul li a{
    padding: 4px 0;
    font-weight: 400;
    letter-spacing: 0.25px;
    font-size:16px;
    text-decoration: underline;
    color: #fff; /* changed */
}


.pth-footer .hs-menu-wrapper.hs-menu-flow-vertical > ul li
{
  margin-left:0;
}

.pth-footer .contact-us .cta_button{
    padding-left:0;
    padding-right:0;
    width:100%;
}

.pth-footer .contact-us p{
    color:#fff; /* changed */
    font-weight:400;
}

.pth-footer .menu-group h5{
    font-weight:600;
    color: #fff; /* added */
}


.pth-footer .bottom-footer{
    margin-top:60px;
    padding-top:40px;
    position:relative;
    border-top: 1px solid #e1e2e2;
    text-align: center;
}


.pth-footer .bottom-footer:before{
    position:absolute;
    content:'';
    top:0;
    left:15px;
    right:15px;
    border-top:1px solid rgba(255,255,255,0.2);
}


.pth-footer .footer-logo{
    margin-bottom:30px;
}

.pth-footer .footer-logo img{
    width:100%;
    max-width:200px;
}

.pth-footer .copyright{
    color:#fff; /* changed */
}

.pth-footer .social-icons a{
    margin:0 8px;
    font-size:18px;
}

.pth-footer .social-icons a:first-child{
    margin-left:0px;
}

.pth-footer  .bottom-menu .hs-menu-wrapper.hs-menu-flow-horizontal > ul li a{
    font-weight: 400;
    letter-spacing: 0.25px;
    padding: 0 0 0 22px;
    text-decoration: underline;
    color: #fff; /* added */
}

@media (min-width:768px) and (max-width:1024px){
    
body .container-fluid .row-fluid .pth-footer .menu-group{
    width:100%;
    margin:0;
}     

body .container-fluid .row-fluid .pth-footer .right-col{
    width:100%;
    margin:0;
}

.pth-footer .social-icons a {
    margin: 0 6px;
    font-size: 16px;
}

.pth-footer .bottom-menu .hs-menu-wrapper.hs-menu-flow-horizontal>ul li a {
    font-size: 12px;
}

.pth-footer .copyright {
    font-size: 12px;
}

.pth-footer .social-icons{
    padding-bottom: 25px;
}

}




@media (max-width:767px){
    
.pth-footer .social-icons{
    margin:20px 0;
}    

.pth-footer .bottom-menu .hs-menu-wrapper.hs-menu-flow-horizontal > ul{
    text-align:center;
}

.pth-footer .bottom-menu .hs-menu-wrapper.hs-menu-flow-horizontal > ul li{
    float: none;
    width: auto;
    display: inline-block;
}

.pth-footer .bottom-menu .hs-menu-wrapper.hs-menu-flow-horizontal > ul li a {
    padding: 0 8px;
    width:auto;
    max-width:none;
}

}



/***************************Hero Style************************************/

.news-blog-banner{
    padding:150px 0px 100px;
}

.overlay.news-blog-banner:before {
    background: rgba(0, 0, 0, 0.40);
}

.hero-banner{
    padding:300px 0px;
}

.overlay.hero-banner:before {
    background: rgba(0, 0, 0, 0.40);
}

.section-banner{
    padding:200px 0px;
}

.overlay.section-banner:before {
    background: rgba(0, 0, 0, 0.40);
}

/*Toggle*/

.subnav{
    width: 100%;
    background: #fff;
    border-bottom: 0px solid #d7d5d5;
    z-index: 10;
    text-align: center;
    padding: 0px;
}

.subnav h2{
    display: inline-block;
    margin: 0px;
    font-size: 25px;
    text-transform: initial;
    font-weight: 600;
}


.click-me.styleno1 {
    padding: 0px 0px 0px;
    text-align: center;
    font-size: 16px;
    line-height: 16px;
    font-weight: 600;
    text-transform: initial;
    vertical-align: middle;
    cursor: pointer;
}


.click-me{
    text-align:left;
    padding: 25px 0 25px;
    padding: 10px 20px;
   
}

.close-button img{
    width:40px;
}

.click-me .fa{
    cursor: pointer;
    font-size: 35px;
    color: #F46419;
    display: inline-block;
    position:relative;
    top: 5px;
}

.blog-sec-body{
    padding-top:55px;
}

@media(min-width:768px){
.news-post-topic{
    border-right: 1px solid rgba(255, 255, 255, 0.5);
}

.blog-sub-form{
    border-right: 1px solid rgba(255,255,255,0.5);
    padding-right: 30px;
}
}

@media(max-width:767px){
    
    .click-me.styleno1{
        font-size:14px;
    }
    
}

/* ==========================================================================
 
                                Blog-Style                            
 
   ========================================================================== */
   

/**/


.overlay{
    position:relative;
    z-index:1;
}

.overlay:before{
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(23,23,23,1);
    background: -moz-linear-gradient(45deg, rgba(23,23,23,1) 0%, rgba(109,0,25,0) 100%);
    background: -webkit-gradient(left bottom, right top, color-stop(0%, rgba(23,23,23,1)), color-stop(100%, rgba(109,0,25,0)));
    background: -webkit-linear-gradient(45deg, rgba(23,23,23,1) 0%, rgba(109,0,25,0) 100%);
    background: -o-linear-gradient(45deg, rgba(23,23,23,1) 0%, rgba(109,0,25,0) 100%);
    background: -ms-linear-gradient(45deg, rgba(23,23,23,1) 0%, rgba(109,0,25,0) 100%);
    background: linear-gradient(45deg, rgba(23,23,23,1) 0%, rgba(109,0,25,0) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#171717', endColorstr='#6d0019', GradientType=1 );
    z-index: -1;
}

.align-center {
    text-align:center;
}

.all-text-white,
.all-text-white h1,
.all-text-white h2,
.all-text-white h3,
.all-text-white p,
.all-text-white h4,
.all-text-white h5,
.all-text-white h6,
.all-text-white li,
.all-text-white .fa,
.all-text-white a{
    color:#fff;
}

.all-text-orange {
   color:#f37223;
}


 .section-padding {
    padding: 3rem 0;
}

.hs-featured-image {
   float:none;
   margin:0px 0px 0px 0px;
   max-width:100%;
   padding-right:0px;
   height:240px;
   width: 100%;
   display: block;
   background-size: cover!important;
   background-position: center center!important;
   position:relative;
}

.hs-featured-image:after{
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    display: block;
    top: 0;
    left: 0;
    right: 0;
    //background-image: linear-gradient(to bottom,rgba(0,0,0,0.2),rgba(0, 0, 0, 0.6));
}

.news-post-header{
    position:relative;
    padding-bottom:7px;
}

.news-post-item h2{
    margin-top:5px;
    margin-bottom:0px;
    padding-bottom:0px;
    display: inline-block; 
}

.news-post-item h2 a{
    line-height: 1.25;
    display: -webkit-box;
    font-size:20px;
    font-weight:600;
    color:#114c8f;
}

.news-post-item h2 a:hover,
.row-fluid .news-post-listing .news-post-item:hover h2 a{
    color: #212121;
}

.news-pubdate {
    width: 100%;
    height: 40px;
    border-radius: 0px;
    background-color: rgba(0, 0, 0, 0.67);
    box-shadow: 0px 0px 9px 0 rgba(0, 0, 0, 0.31);
    padding-top: 10px;
    position: absolute;
    top: -40px;
    z-index: 10;
    text-align: left;
    left: 0px;
    padding-right: 15px;
    padding-bottom: 15px;
    padding-left: 15px;
    box-sizing: border-box;
}

.events-pubdate {
    font-size: 0.9em !important;
    font-weight: 600 !important;
    color: #00b6a8;
    text-transform: uppercase;
}

span.day {
    font-size: 18px;
    display: inline-block;
    line-height: 1;
    font-weight: bold;
    color: #ffffff;
}

span.month,span.year {
    font-size: 12px;
    line-height: 1;
    color: #ffffff;
    font-weight: 600;
    text-transform: uppercase;
}

@media(min-width:768px){

.news-post-listing{
    display: block;
    margin: 0 -25px 0 0;
    padding: 0;
    //overflow: hidden;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-direction: normal;
    -moz-box-direction: normal;
    -webkit-box-orient: horizontal;
    -moz-box-orient: horizontal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -moz-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-align-content: stretch;
    -ms-flex-line-pack: stretch;
    align-content: stretch;
    -webkit-box-align: start;
    -moz-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
}

.row-fluid .news-post-listing .news-post-item {
    border: solid 1px #e4e4e3;
    float: left;
    width: 30%;
    display: block;
    box-sizing: border-box;
    margin: 0 2.5% 25px 0;
    list-style: none;
    padding-bottom: 0;
    display: block;
    box-sizing: border-box;
    align-self: stretch;
    transition: all ease-in-out .4s;
    background: #fff;
    border-radius: 5px 5px 5px 5px;
    position:relative;   
}

}

.row-fluid .news-post-item {
    border: solid 1px #e4e4e3;
    float: left;
    width:100%;
    display: block;
    box-sizing: border-box;
    margin: 0 0px 25px 0;
    box-sizing: border-box;
    transition: all ease-in-out .4s;
    background: #fff;
    border-radius: 5px 5px 5px 5px;
    position:relative;
}

.hs-featured-image{
     transition: all 5s cubic-bezier(0.5,0.51,1,1);
    -webkit-transition: all 10s cubic-bezier(0.5,0.51,1,1);
}
    
.news-post-item:hover .hs-featured-image{
    transform: scale(1.3);   
}   

.row-fluid .news-post-listing .news-post-item:hover{
    border-color: #e4e4e3;
    box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.26);    
}

.event-listing-height {
  height: 300px !important;
}

@media (max-width: 768px) { 
.event-listing-height {
  height: 350px !important;
}
}

.news-post-body {
    padding: 25px 15px 50px;
    position: relative;
}

.news-post-item .bottom-part{
    display:inline-block;
    width: 100%;
    padding: 20px 0 10px;
    border-top: 1px solid #e4e4e3;
    margin-top:10px;
}

.news-post-item .hs-author-avatar{
    display: block;
    max-width:40px;
    float: left;
}

.author-module {
    position: absolute;
    bottom: 25px;
    padding: 20px 15px;
    width: 100%;
    box-sizing: border-box;
}

.author-module a.author-link, .author-module {
    font-size: 14px;
    font-weight: 600;
    color: #999;
    line-height: 1.4;
    display: inline-block;
    max-height: 28px;
    transition: color .15s;
}
    
.authorname {
    position: relative;
    top: 5px;
    font-size: 10px;
    color: #fff;
    font-weight: 600;
    padding-left: 15px;
    letter-spacing: .75px;
    text-transform: uppercase;
}

.author-box{
    position: absolute;
    bottom: 0px;
}

.social-shares{
    overflow: hidden;
    display: block;
    text-align: center;
}

.social-shares .fa{
    font-size: 20px;
    padding: 0px 8px;
    border-radius: 50%;
    height: 20px;
    display: inline-block;
    
}

.social-shares .fa:hover{color:#F46419;}

.s-title{
    display: block;
    font-size: 11px;
    font-weight:700;
    padding-bottom: 7px;
    text-transform: uppercase;
   
}
    
.news-post-item .hs-author-avatar img{
    border-radius:50%;
} 

.hs-featured-image-wrapper{
    position:relative;
    overflow: hidden;
    border-radius: 5px 5px 0px 0px;
}

.hs-featured-image-wrapper .topic-list a:first-child + a{
    display: inline-block;
    color: #f2f2f2;
    border: 1px solid #f2f2f2;
    padding: 7px 10px;
    font-size: 12px;
    text-transform: uppercase;
    position: absolute;
    top: 10px;
    right: 10px;
    font-weight: 600;
   
}

.hs-featured-image-wrapper .topic-link{display:none;}

.news-post-body p{
    margin-top: 0px;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 300;
}

.news-post-topic  ul{
  list-style: none;
  padding: 0px;
  margin: 0px;
}

.news-post-topic  ul li{
    display: none;
    margin-bottom: 5px;
    margin-right: 2px;
    margin-left: 0px;
    background-color: #fff;
    cursor: default;
}

.news-post-topic  ul li:hover{
    background-color: #F7CB7B;
}

.news-post-topic  ul li a{
  font-weight: 600;
  color:#191919;
  letter-spacing:.5px;
  font-size:12px;
  border: 0px solid #F7CB7B;
  padding:7px 20px;
  display: block;
  text-transform:uppercase;
}

.news-post-topic  ul li a:hover{
    border: 0px solid #F7CB7B;
    color:#191919;
}

.recents-news-post ul{
  list-style: none;
  padding: 0px;
  margin: 0px; 
}

.recents-news-post ul li{position:relative;padding-left:25px}

.recents-news-post ul li:before{
    content: "\f00c";
    position: absolute;
    left:0px;
    top:0px;
    font-family: "FontAwesome";
    width:18px;
    height:18px;
    color: #F46419;
    font-size: 15px;
}

.recents-news-post ul li a{
    color:#ffffff;
    font-size:15px;
}

.top-section-blog{
    //padding-top:50px;
    //background:url("//459002.fs1.hubspotusercontent-na1.net/hubfs/459002/brickwall.png") repeat;
    //padding-bottom: 20px;
    //transform: translate(0,0);
    //transition: transform .4s ease-in,opacity .3s ease-in;
}

.top-section-blog::-webkit-scrollbar {
    width: 0px;  /* remove scrollbar space */
    background: transparent;  /* optional: just make scrollbar invisible */
}

.top-section-blog h3{
    font-size: 24px;
    margin-top: 0px;
    color:#fff;
    font-weight: 400;
    text-align:left;
}


.top-section-blog form input[type="email"]{
    height:50px;
    padding-left:15px;
    margin-bottom:20px;
    font-size:15px;
    border:1px solid #e7e7e7;
}

.top-section-blog form input[type="email"]:focus{
    outline:none;
}

.top-section-blog form label{display:none;}

.top-section-blog form ul.hs-error-msgs.inputs-list{
    margin:0px;
    border:2px solid #F46419;
    list-style-type:none;
    width:100%;
    position: absolute;
    top: 49px;
}

.top-section-blog .hs_email.field.hs-form-field{position:relative;overflow:hidden;}

.top-section-blog form input[type="submit"]{
    border: none;
    padding: 10px 20px;
    text-align: center;
    display: block;
    width: 100%;
    font-weight: bold;
    background:#000;
    color: #fff;
    cursor: pointer;
    height:50px;
    -webkit-appearance:none;
    font-size: 16px;
    text-transform: uppercase;}
    
  
    
/* Placeholder Text */
::-webkit-input-placeholder { /* Webkit Browsers */
   color: #555;
   opacity: 1;
   font-size:15px;
   font-style:italic;
   }
:-moz-placeholder { /* Firefox 18- */
   color: #555;
   opacity: 1;
   font-size:15px;
   font-style:italic;
}
::-moz-placeholder { /* Firefox 19+ */
   color: #555;
   opacity: 1;
   font-size:15px;
   font-style:italic;
}
:-ms-input-placeholder { /* IE10 */
   color: #555;
   opacity: 1;
   font-size:15px;
   font-style:italic;

}


/*Blog news-post page Fadding Effects*/

body.hs-blog-post .body-container-wrapper {
animation: fadein 2s;
    -moz-animation: fadein 2s; /* Firefox */
    -webkit-animation: fadein 2s; /* Safari and Chrome */
    -o-animation: fadein 2s; /* Opera */
}
@keyframes fadein {
    from {
        opacity:0;
    }
    to {
        opacity:1;
    }
}
@-moz-keyframes fadein { /* Firefox */
    from {
        opacity:0;
    }
    to {
        opacity:1;
    }
}
@-webkit-keyframes fadein { /* Safari and Chrome */
    from {
        opacity:0;
    }
    to {
        opacity:1;
    }
}
@-o-keyframes fadein { /* Opera */
    from {
        opacity:0;
    }
    to {
        opacity: 1;
    }
}

.section.news-post-header h2,.section.news-post-header h2 a{
    font-size:40px;
    line-height:50px;
    font-weight:400;
    text-align:center;
    margin-top:0px;  
}

.hot-post-header.have-featured-image {
    background-size: cover;
    background-position: center;
    padding:150px 0px;
    display: flex;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    justify-content: center;
    -webkit-justify-content: center;
    justify-content: center;
    text-align:left;
    position:relative;
    z-index:1;
    width: 1000px;
    height:600px;
    margin-left: auto;
    margin-right: auto;
}

@media (max-width: 767px) {
.hot-post-header.have-featured-image {
    width: 100%;
}
}

.hot-post-header:before{
    content: "";
    position: absolute;
   // background: rgba(0, 0, 0, 0.3);
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index:-1;
}

.blog-post-header ,h2,h4,a {
  text-decoration: none;
  color: ; /* changed */
  text-align: ; /* changed */
  padding: 10px 0px;  /* changed */
  line-height:1.25;
 }


span.blog-listing-item-container {
    display: block;
    max-width: 770px;
    margin: 0 auto;
}

span.blog-listing-item-container a{
    text-decoration: none;
}

h1.blog-post-title {
    margin: 0px;
}  
    
.hs-blog-post .widget-span.widget-type-cell.blog-content{
    margin:0 auto;
    float:none;
    max-width:900px;
    padding:0px 20px;
    position: relative;
    z-index: 1;
}   

.hs-blog-post .section.news-post-body{
  padding-top:25px;
}

.hs-blog-post .section.news-post-body p,
.hs-blog-post .section.news-post-body{
    font-size:18px;
    line-height:30px;
}
    
.hs-blog-post .section.news-post-body h3,
.hs-blog-post .section.news-post-body h2{
    font-size:34px;
    line-height:40px;
    text-align:left;
    font-weight:400;
}   
    
.hs-blog-post .section.news-post-body ul{
  list-style: none;
  padding: 0px;
  margin: 25px 0px;
}

.hs-blog-post .section.news-post-body ul li{
  list-style: none;
  position:relative;
  padding-left:25px;
  padding-bottom:5px;
  margin-left:0px;
}

.hs-blog-post .section.news-post-body ul li:before{
    content: "\f105";
    position: absolute;
    left: 0px;
    font-family: "FontAwesome";
    width: 18px;
    height: 18px;
    font-size: 16px;
}

.hs-blog-post .section.news-post-body img{//display:none;
    margin-top:20px;
    margin-bottom:20px;
}

.hs-blog-post #blog-sec-body{
    padding:25px 0px;
    position:relative;
}
    
.news-post-share{
   position: relative;
    top: -27px;
    left: 10px;
}

.news-post-share ul{
list-style:none;
  padding:0px;
  margin:0px;}
 
.news-post-share li{
    display:block;
    width:40px;
    height:40px;
    text-align: center;
    vertical-align: middle;
    margin-left:0px;
}

.news-post-share li a {
    color: #2d3e50;
}

.news-post-share li a:hover,.news-post-share li:hover .fa{color:#F46419;}

.news-post-share li .fa{
    font-size:24px;
    vertical-align: middle;}

.news-post-share {
    position: absolute;
    top: 30px;
    left: -60px;
    float: left;
    height: 100%;
}

/*Author-Style*/

.author-post-details{
    overflow:hidden;
}

span.author-avatar {
    width: 65px;
    height: 65px;
    display: block;
    background-size: cover;
    background-position: top center;
    background-repeat: no-repeat;
    border-radius: 100%;
    margin-left:30px;
    float: left;
    border: 0px solid #F46419;
}

span.authorname-details {
    padding-top:18px;
    display: block;
    margin-left: 20px;
    float: left;
    font-size: 14px;
}

a.topic-link {
    font-size: 12px;
    font-weight: 500;
    color: #999;
    text-transform: uppercase;
    text-decoration: none;
    font-family: 'Montserrat',Sans-Serif;
    letter-spacing: 1px;
}

span.min-read {
    font-size: 12px;
    font-weight: 500;
    color: #999;
    text-transform: uppercase;
    text-decoration: none;
    font-family: 'Montserrat',Sans-Serif;
    letter-spacing: 1px;
}

.for-topic-text{
    color:#03799a;
    font-size:16px;
}

.more-link{
  float: none;
  text-align: center;
  padding: 8px 0px;
  display: inline-block;
  background: #F46419;
  color: #fff;
  font-weight: 600;
  margin: 20px auto 0px;
  width: 100%;
  max-width: 100%;
  font-size: 16px;  
}

.news-post-date{
    font-size: 12px;
    color: #fff;
    z-index: 1;
    position: relative;
    font-weight: 700;
    background-color:#F46419;
    padding: 10px 10px;
    display: inline-block;    
    text-transform: uppercase;
}

.news-post-date .fa{font-size:15px;}

/*Recent news-posts*/

.hs-rss-item.hs-with-featured-image .hs-rss-item-text {
width: 80%;
padding-right:10px;
line-height: 20px;
font-weight:600;
}

.hs-rss-item.hs-with-featured-image .hs-rss-item-text a{
 color:#191919;
}

.hs-rss-item.hs-with-featured-image .hs-rss-item-text a:hover{
 color:#F46419; 
}

.popular ul li a:hover,.hs-rss-item-text a:hover{color: #8cc647;}

.hs-rss-item.hs-with-featured-image .hs-rss-item-image-wrapper {width: 100%;}

.hs-rss-byline{opacity: 1;font-size: 10px;color: #03799a;text-transform: capitalize;font-weight:bold;}

.popular ul{list-style:none;padding-left:0px;}

.popular ul li{line-height: 20px;
padding-bottom: 10px;
position: relative;
padding-left: 30px;}

.popular ul li:before,.popular ul li:before {
content: "\f00c";
position: absolute;
left: 5px;
top: 2px;
font-family: "FontAwesome";
width: 16px;
height: 16px;
color: #8cc647;
}

.popular ul li a{font-size:14px;}

/**/

.blog-subscription{background: #f2f3f4;padding: 10px;border-top:3px solid #8cc647;padding-bottom:30px;}

.blog-subscription label,.blog-subscription .hs-form-required{display:none;}

.blog-subscription input[type="email"]{text-align: left!important;
min-height: 36px;
border: 1px solid #ccc;}

.blog-subscription .hs-button{border: none;
padding: 10px 20px;
text-align: center;
display: block;
width: 100%;
margin-top: 5px;
font-weight: bold;
background: #;
background: #34495e;
color: #fff;
cursor:pointer;}

.hs-button:hover{background:#0F2030;}

#comment-form{
  padding-top:15px;
  padding-bottom:0px;
  padding-left: 15px;
  padding-right: 15px;
 }
 
#comment-form .hs-form-field {
    position: relative;
} 

#comment-form ul.hs-error-msgs.inputs-list {
    list-style: none;
    padding: 0;
    margin:0px;
}

#comment-form ul.hs-error-msgs.inputs-list label {
    font-size: 13px;
    color: red;
    font-weight: 600;
    letter-spacing: .25px;
}

#comment-form input[type="email"],
#comment-form input[type="text"]{
    text-align: left!important;
    min-height:50px;
    border:1px solid #ccc;
    border-radius:0px;
    font-size:16px;
    width:100%;
    margin-bottom:20px;
    padding-left:16px;
    box-sizing: border-box;
    max-width: 100%;
    box-shadow: none;
 }
 

#comment-form .hs-form fieldset{max-width:100%;} 

#comment-form textarea{
    padding-top:15px;    
    padding-left:16px;
    border-radius:0px;
    text-align: left!important;
    min-height:120px;
    border: 1px solid #ccc;
    width:100%;
    font-size:16px;
    box-sizing: border-box;
    max-width: 100%;
    box-shadow: none;
 }

#comment-form .hs-form-booleancheckbox input{min-height:10px;}

#comment-form .hs-button{
    border: none;
    cursor: pointer;
    float: none;
    text-align: center;
    padding: 16px 0;
    display: inline-block;
    background: #F46419;
    color: #fff;
    font-weight: 600;
    margin:0px auto 0;
    width: 100%;
    max-width: 100%;
    font-size: 16px;
    -webkit-appearance: none;
    -webkit-transition: all .4s ease-in-out;
    -moz-transition: all .4s ease-in-out;
    -o-transition: all .4s ease-in-out;
    -ms-transition: all .4s ease-in-out;
     transition: all .4s ease-in-out;
}




#hubspot-topic_data{
    border-top: 1px solid #e1e3e4;
    border-bottom: 1px solid #e1e3e4;
    padding: 10px 0;}


/*Pagination-Style*/


.previous-news-posts-link {
    font-size: 30px;
    
    padding: 10px 20px;
    color: #fff;
    font-weight: 600;
    position: fixed;
    left:0px;
    top: 65%;
    -webkit-appearance: none;
    -webkit-transition: all .4s ease-in-out;
    -moz-transition: all .4s ease-in-out;
    -o-transition: all .4s ease-in-out;
    -ms-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out;
}

.next-news-posts-link {
    font-size: 30px;
    background:#F46419;
    padding: 10px 20px;
    color: #fff;
    font-weight: 600;
    position: fixed;
    right: 0px;
    top: 65%;
    -webkit-appearance: none;
    -webkit-transition: all .4s ease-in-out;
    -moz-transition: all .4s ease-in-out;
    -o-transition: all .4s ease-in-out;
    -ms-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out;
    
}

.news-post-pagination{}

.prev-news-post a {
    position:fixed;
    font-weight: 700;
    left: 0;
    top:65%;
    color: #ffffff;
    background-color:#F46419;
    padding: 15px 20px 10px;
    font-size: 12px;
    vertical-align: middle;
    -webkit-appearance: none;
    -webkit-transition: all .4s ease-in-out;
    -moz-transition: all .4s ease-in-out;
    -o-transition: all .4s ease-in-out;
    -ms-transition: all .4s ease-in-out;
    transition: all .4s ease-in-out;
    

}



.prev-news-post .fa{
  padding-right: 7px;
  font-size: 40px;
  padding-top: 0;
  position: relative;
  top: -4px;
  vertical-align: middle;
}

.next-news-post a{
  position:fixed; 
  font-weight: 700;
  right:0px;
  top:65%;
  color: #ffffff;
  background-color:#F46419;
  padding: 15px 20px 10px;
  font-size: 12px;
  vertical-align: middle;
  -webkit-appearance: none;
  -webkit-transition: all .4s ease-in-out;
  -moz-transition: all .4s ease-in-out;
  -o-transition: all .4s ease-in-out;
  -ms-transition: all .4s ease-in-out;
  transition: all .4s ease-in-out;
  
}

.next-news-post .fa{
  padding-left:7px;
  font-size: 40px;
  padding-top: 0;
  position: relative;
  top: -4px;
  vertical-align: middle;
}


.news-post-by-date{
  padding: 20px 0px;
  font-size: 14px;
  
  text-align: center;
  margin-top: 0;
  
  font-weight: 700;
  text-transform: uppercase; 
  letter-spacing: 1px;
 }

.news-post-by-date .fa{
    color:#F46419;
    font-size:20px;
}

.hs-author-avatar{
    text-align:center;
}

.hs-blog-post .hs-author-avatar img{
    border-radius:50%;
    border:4px solid #cccccc;
}

.hs-blog-post .hs-author-avatar a{
    display: inline-block;
}

body.hs-blog-post .blog-author-section{
    display:none;
}

body.hs-blog-listing .blog-author-section{
    display:none;
}

body.hs-blog-listing.author-body .blog-author-section{
    display:block;
    background-color:#fff;
    padding:75px 0px;

}
    
body.hs-blog-listing.author-body .news-post-item .bottom-part,
body.hs-blog-listing.author-body .news-post-body{
 
}    

body.hs-blog-listing.author-body .hs-author-profile{text-align:center;}

body.hs-blog-listing.author-body .hs-author-avatar img{
    max-width:100px;
    border-radius:50%;
}

body.hs-blog-listing.author-body h2.hs-author-name{
    font-size:25px;
    color:#191919 ;
}
    
body.hs-blog-listing.author-body .hs-author-social-links{padding-bottom:15px;}    

body.hs-blog-listing.author-body .hs-author-bio{
    max-width:770px;
    margin:0 auto;
    float:none;
    color:#191919 ;
    font-size:16px;
    line-height:25px;
    
}


body.hs-blog-listing.author-body .hs-author-social-links a.hs-author-social-link{
    background-image:none;
    width: 30px;
    height: 30px;
    border-width: 0;
    border: 0;
    line-height: 24px;
    background-size: 24px 24px;
    background-repeat: no-repeat;
    display: inline-block;
    text-indent: 0px;
    font-size: 20px;
    color: #ffffff;}
    
body.hs-blog-listing.author-body .hs-author-social-links a.hs-author-social-link:hover{color:#F46419;}    


/* ==========================================================================
   Responsive-Style
   ========================================================================== */
   
   
   
   
  @media(max-width:1024px) and (min-width:768px){
      
    .news-post-item ,.news-post-listing-simple{width:50%} 
    
    .next-news-post a,.prev-news-post a,.next-news-posts-link,.previous-news-posts-link{font-size:0px;z-index:3;}
    

}


@media(max-width:767px){
     
body .header-container-wrapper .hs-menu-wrapper{
    display:none;
}

.row-fluid .news-post-share{
   display: contents;
}

  .news-post-share ul{
   display:inline-flex;
        margin: 20px;
  }
  
.recents-news-post ul li {
    padding-bottom:10px;
}

.news-post-item ,
.news-post-listing-simple{
    width:100%;
    padding-right:0px;
    margin:0px;
}

.news-post-item .hs-author-avatar{
    max-width:35px;
}

.authorname{
    font-size:11px;
    top:8px;
}

.span3.pop-news-post-image {
    float: left;
    width: 50px;
    margin-right: 15px;
}

.span9.pop-news-post-title {
    float: left;
    width: 70%;
}

.span9.pop-news-post-title a {
    font-size: 14px;
}


.news-post-listing{
    margin:0px;
}

.section.news-post-body{
    padding:0px;
}

.row-fluid blockquote{
  padding-left:15px; 
  margin: 30px 0;
}

.widget-type-blog_subscribe,
.recents-news-post{padding-top:40px;}

.section.news-post-header h2, .section.news-post-header h2 a {
font-size: 30px;
line-height: 40px;}

.next-news-post a,.prev-news-post a,.next-news-posts-link,.previous-news-posts-link{font-size:0px;z-index:3;padding: 10px 10px 5px;}

.next-news-post a .fa,.prev-news-post a .fa,.next-news-posts-link .fa,.previous-news-posts-link.fa{font-size:16px;top: -3px;}

#comment-form {
padding-top:0px;
padding-bottom: 0;
padding-left:0px;
padding-right:0px;
}

.hs-blog-post #comment-form div.input{margin-right:0px;}

.hs-blog-post .section.news-post-body h3, .hs-blog-post .section.news-post-body h2{
    font-size:24px;
    line-height:32px;
    
}

.span3.pop-news-post-image{
    width: 23.404255317%;
}    
   
  
}
   
   
@media(max-width:767px){

.header-container .hs_cos_wrapper_type_logo a img{
    max-width: 70px;
    padding-top: 0px;
    padding-left: 0px;
    margin-top: -10px;
    margin-left: -10px;
}

.header-container-wrapper{
    position:relative;
    box-shadow:none;
    border-bottom: 1px solid #e2e2e3;
}

.header-container.container-fluid .wrapper{
    padding-right:0px;
    padding-left:0px;
}

.row-fluid .subnav{
    position:relative;
    width:100%;
    top:0px;
}

.body-container-wrapper {
    padding-top:0px;
}

.row-fluid .breadcrumb-section{
    display:none;
    visibility:hidden;
}

.blog-section{
    padding-top:15px;
    padding-right:0px;
}

.blog-content{
    padding-right:0px;
}

.news-post-body .hs-featured-image{
    float:none;
    width:100%;
    margin:0 auto 30px;
    max-width:100%;
}

.section.news-post-header h2 {
    font-size: 25px;
    line-height: 32px;
}

.hs-blog-post .section.news-post-body img:first-child {
    margin-bottom: 0px;
}

}




.hamburger-menu .click-me .fa{
    
}

body.hamburger-menu .row-fluid .pth-header{
    z-index:0;
}

.hamburger-menu .row-fluid .top-section-blog{
    visibility: visible;
    -webkit-transform: translate3D(0, 0, 0);
    transform: translate3D(0, 0, 0);
    -webkit-transition: -webkit-transform 0.5s;
    transition: transform 0.5s;
}

.row-fluid .top-section-blog{
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: #F46419;
    background-image: linear-gradient(180deg,#f07155,#ff8f59);
    padding-top:100px;
    visibility: hidden;
    overflow: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-transform: translate3D(0, -100%, 0);
    transform: translate3D(0, -100%, 0);
    -webkit-transition: -webkit-transform 0.5s,visibility 0.5s;
    transition: transform 0.5s,visibility 0.5s;
    z-index:99;
}


.row-fluid .hamburger-menu .click-me{
    
}

.subnav .click-me{
    padding-top:0px;
}


.close-button.click-me {
    position: absolute;
    top: 30px;
    right: 30px;
    max-width: 30px;
    padding: 0px;
    cursor: pointer;
}


/*Sidebar*/

.pop-news-post-image{
    
}

.pop-news-post-item {
    overflow: hidden;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.46);
    margin-bottom: 15px;
}

.pop-news-post-item:last-child{
    border-bottom:none;
    margin-bottom:0;
}

.span3.pop-news-post-image {
    max-width:100px;
    float:left;
    background-size: cover !important;
    display: block;
    background-position: top center !important;
    height: 75px;
    
}

.pop-news-post-title a{
    font-size: 16px;
    line-height: 20px;
    text-decoration: none;
    display: inline-block;
    padding-left:0px;
    color:#ffffff;
}

.pop-news-post-date {
    font-size: 11px;
    color: #fff;
    text-align: left;
    display: block;
    font-weight: bold;
    text-transform: uppercase;
    margin-top: 5px;
}


blockquote {
    border-left: 5px solid #F46419;
    margin: 50px 0px;
    padding-left: 30px;
}


.author-body .author-box{
    display:none;
}


@media(min-width:768px){
    
.make-equal-height > .row-fluid-wrapper >.row-fluid{
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    
}
    
}


.hs-blog-listing .blog-sec-body {
    padding-top: 70px;
    padding-bottom:70px;
}


.comment-section .row-fluid .widget-type-cell.wrapper-wrapped {
    max-width: 770px;
    margin: 0 auto;
    float: none;
}

.comment-section h3{
    font-size:30px;   
}

.hs-blog-post .row-fluid .post-footer {
    padding:0px;
    background:transparent;
    border:0;
    margin-top:0;
}

.comment-section{
    position:relative;
}

.comment-section:before {
    content: "";
    position: absolute;
    display: block;
    width: 38px;
    height: 38px;
    -webkit-transform: rotate(-315deg);
    transform: rotate(-315deg);
    background-color: #fff;
    left: 0;
    right: 0;
    margin: 0 auto;
    top: -19px;
}

.post-title-related{
   line-height: 1.25;
   display: -webkit-box;
   font-size: 20px;
   font-weight: 600;
}

.main-list .post-body {
    padding: 20px 20px;
}

.related-post .post-item.span4 {
    padding-right: 0px;
    border: solid 1px #e4e4e3;
    margin-bottom: 25px;
    transition: all .3s ease-in-out;
    background: #ffffff;
    position: relative;
    border-radius: 5px 5px 5px 5px;
}

.related-post .post-item.span4:hover{
    box-shadow: 0px 2px 11px 0px rgba(0, 0, 0, 0.26);
}


.main-list.post-item-inner p{
    margin-top: 0px;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 300;
}

h3.related-post-title {
    margin: 0px auto 40px;
    max-width: 600px;
    font-size:30px;
    color: #2d3e50;
}

.main-list.post-item-inner a.read-more-link {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;

}

.custom-next {float : right;}
.custom-previous {float:left;}
.custom_blog_next-previous a:hover {
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
}
.custom_blog_next-previous  a {
    text-decoration: none;
}



.section.news-post-body p:first-child::first-letter {
    /*
    font-size: 60px;
    color:#F46419;
    font-weight: bold;
    padding-right: 10px;
    line-height: 100%;
    float: left;
  */
}

    
 .span12.widget-span.widget-type-cell.subscribe-card.news-post-item {
    //background-color: #007fe4;
    background: linear-gradient(180deg,#f19961 0,#f97a67);
 }
    


.subscribe-card .news-post-item-inner {
   padding: 120px 20px 80px;
}

body .subscribe-card h3.form-title {
    text-align: center;
    margin-top: 10px;
    font-size: 20px;
    color:#ffffff;
}

body .widget-type-blog_subscribe form {
    position: relative;
    min-height: 70px;
}

.span12.widget-span.widget-type-blog_subscribe {
    min-height: 70px!important;
}

li.shareTrigger, li.shareTrigger:hover .fa {
    color: #000;
}


.footer-cta-section.overlay:before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.35);
    z-index: -1;
}

.footer-cta-section .hs_cos_wrapper.form-title {
    display: none;
}

body .footer-cta-section .widget-type-blog_subscribe form input[type="email"]{
    margin-bottom:10px;
}

.footer-cta-section .row-fluid form .field>label{
    display:none;
}

.footer-cta-section .row-fluid [class*="span"].wrapper{
    max-width:770px;
}


.footer-cta-section p{
    font-size:18px;
}

body .footer-cta-section .row-fluid form .hs-button.primary ,
body .container-fluid .footer-cta-section .row-fluid form .hs-button.primary {
    height: auto;
    margin-top: 0px;
    margin: 0px;
    padding: 14px 50px;
    cursor: pointer;
    background: #222;
    border: 1px solid #222;
    color: #fff;
    border-radius: 0px;
    width: auto;
    position: static;
    font-weight: 400;
    height: 50px;
    font-size: 14px;
    letter-spacing: 1px;
}

body .container-fluid .footer-cta-section .row-fluid form .hs-button.primary:hover,
body .footer-cta-section .row-fluid form .hs-button.primary:hover {
    background-color: #ffffff;
    border-color: #fff;
    color: #222;
}

.footer-cta-section .hs_email.field.hs-form-field {
    width: 70%;
    float: left;
}

.footer-cta-section .widget-type-blog_subscribe{
    padding-top:30px;
}

@media(max-width:767px){
.row-fluid .footer-cta-section .hs_email.field.hs-form-field {
    width: 100%;
    float: none;
}
.container-fluid .row-fluid .wrapper{
    box-sizing: border-box;
}

.comment-section .row-fluid .widget-type-cell.wrapper-wrapped,.header-container.container-fluid .wrapper{
    padding: 0px 20px;
}

.hs-blog-post .widget-span.widget-type-cell.blog-content{
    padding: 0px 0px;
}

}

.hs-author-profile .hs-author-avatar {
    width: 90px;
    max-width: 90px;
    height: 90px;
    overflow: hidden;
    border-radius: 50%;
    margin: 0 auto;
    margin-bottom: 20px;
    border: 5px solid #e7e7e7;
    background-size: cover;
    background-position: top center;
}






.blog-subscription-bar h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
    margin-top: 12px;
    color: #fff;
    letter-spacing: .5px;
    line-height: 25px
}

.blog-subscription-bar .hs_cos_wrapper.form-title {
    display: none
}

body .widget-type-blog_subscribe form input[type="email"] {
    border: 1px solid #eee;
    height: 50px;
    -webkit-appearance: none;
    box-shadow: none;
    border-radius: 0;
    margin-bottom: 0;
    font-size: 15px;
    padding-left: 15px;
    padding-right:110px;
}

body .widget-type-blog_subscribe form {
    position: relative
}

body .container-fluid .row-fluid .widget-type-blog_subscribe form label {
    display: none
}

body .container-fluid .row-fluid .widget-type-blog_subscribe form input[type="submit"] {
    background: #333;
    border: 2px solid #fff;
    border-radius: 0;
    height: 50px;
    width: 105px;
    color: #fff;
    cursor: pointer;
    font-weight: 700;
    letter-spacing: .25px;
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    text-align: center;
    -webkit-appearance: none;
    font-size: 15px;
    margin: 0
}

.hs-form-field {
    position: relative
}

ul.hs-error-msgs.inputs-list {
    padding: 0;
    margin: 0;
    list-style: none
}

ul.hs-error-msgs.inputs-list label {
    font-size: 14px;
    color: red;
    display: inline-block
}

.blog-subscription-bar {
    background: #2b2b2b;
    padding: 15px 0
}

.section.post-body blockquote {
    padding-top: 40px;
    padding-bottom: 40px;
    border-left-width: 5px;
    margin: 50px 0
}

/****************************** Email pref styling *******************************/

.widget-type-email_subscriptions h1, 
.widget-type-email_subscriptions h2 {
  text-align: center;
}

.checkbox-row input[type="checkbox"] {
    padding: 10px;
    border-radius: 4px;
    background: #fff;
    margin-left: 0;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    color: #cfd3d7;
    -webkit-appearance: none;
    border: 2px solid #ddd;
    margin-right: 8px;
    top: 2px;
    width: 20px;
    height: 20px;
}

.checkbox-row input[type="checkbox"]:checked:after {
    content: '';
    position: absolute;
    width: 9px;
    height: 5px;
    top: 5px;
    left: 4px;
    border: 3px solid #f37223;
    border-top: 0;
    border-right: 0;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
}

.subscribe-options input[type="checkbox"] {
    padding: 10px;
    border-radius: 4px;
    background: #fff;
    margin-left: 0;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    color: #cfd3d7;
    -webkit-appearance: none;
    border: 2px solid #ddd;
    margin-right: 8px;
    top: 2px;
    left: 5px;
    width: 20px;
    height: 20px;
}

.subscribe-options input[type="checkbox"]:checked:after {
    content: '';
    position: absolute;
    width: 9px;
    height: 5px;
    top: 5px;
    left: 4px;
    border: 3px solid #f37223;
    border-top: 0;
    border-right: 0;
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
}

.subscribe-options label {
    display: flex;
    align-items: center;
}

.subscribe-options label span {
    margin-left: 10px;
}

.email-prefs .item .item-inner * {
    color: #444;
}

.email-prefs .item p {
    clear: both;
    margin: 5px 40px 0px;
}

.email-prefs .item .item-inner.selected {
    background: #f0f7fc;
    border: 1px solid #e0e7ec;
}

.email-prefs .item .item-inner.highlighted-subscription.selected {
    background: #fff1ee;
}

/****************************** Infinite Scroll Style *******************************/


.ias-trigger.ias-trigger-next,.ias-spinner {
    width: 100%;
    margin: 0 auto;
    margin-top: 40px;
}

.ias-trigger.ias-trigger-next a {
    color: #F46419;
    border: 1px solid #F46419;
    border-radius: 2px;
    display: inline-block;
    padding: 15px 50px;
    text-transform: uppercase;
    font-weight: 700;
    font-family: 'Montserrat',sans-serif;
    letter-spacing: 1px;
    transition: all .4s ease;
}


.ias-trigger.ias-trigger-next a:hover{
    background:#F46419;
    color:#ffffff
}

button.back-to-top{
  margin: 0 !important;
  padding: 0 !important;
  background: #fff;
	height: 0px;
  width: 0px;
  overflow: hidden;
	border-radius: 50px;
	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
  color: transparent;
	clear: both;
  visibility: hidden;
  position: fixed;
  cursor: pointer;
  display: block;
  border: none;
  right: 50%;
	bottom: 75px;
  font-size: 0px;
  outline: 0 !important;
  z-index: 99;
  transition: all .3s ease-in-out;
}
button.back-to-top:hover,
button.back-to-top:active,
button.back-to-top:focus,{
  outline: 0 !important;
}
button.back-to-top::before {
  content: "\f077";
  font-family: "FontAwesome";
  display: block;
  vertical-align: middle;
  margin: -5px 0 auto;
}
button.back-to-top.show {
  display: block;
  background: rgba(192,192,192,0.3);
  color: #fff;
  font-size: 25px;
  right: 50%;
	bottom: 10px;
  height: 50px;
  width: 50px;
  visibility: visible;
	box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.25);
}
button.back-to-top.show:active {
  box-shadow: 0px 4px 8px 2px rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: 0px 4px 8px 2px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 4px 8px 2px rgba(0, 0, 0, 0.25);
}

.top-notif {
  padding: 1em 0 !important;
  font-size: 1.2rem;
  font-weight: 500;    
}

.top-notif a {
  color: #fff;
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;    
}

.no-box {
  display: none;
}
  
    .advisory-banner{
    padding-top: 10px;
    padding-bottom: 3px;
  }

/*new css*/

   #mobile-banner-img	 {
    display: none;
}
  
  @media (max-width: 769px) {
 #desktop-banner-img {
    display: none;
}
 #mobile-banner-img	 {
    display: block;
}
    }
  
.hs-rss-module {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  justify-content: center;
  position: relative;
}

.hs-rss-module > .hs-rss-item {
  width: 33.33%;
}
.hs-rss-item {
  box-sizing: border-box;
  flex-grow: 1;
  width: 100%;
  overflow: hidden;
  list-style: none;
  padding: 0.25em;
  background-color: white;
  border-color: 5px solid #fff;
}

@media (max-width:768px){
  
  .hs-rss-module > .hs-rss-item {
  width: 100% !important;
  }

}  

.hs-with-featured-image
{
  position: relative;
  margin: 2px auto;
  height: 450px;
  width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;    
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;  
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
  filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='.myBackground.jpg', sizingMethod='scale');
  -ms-filter: "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='myBackground.jpg', sizingMethod='scale')";
}
  
.hs-rss-item .hs-with-featured-image .hs-rss-item-image-wrapper {
 width: 100% !important; 
}

.hs-with-featured-image img
{
    object-fit: cover;
  width: 100%;
    width: -moz-available;          /* WebKit-based browsers will ignore this. */
    width: -webkit-fill-available;  /* Mozilla-based browsers will ignore this. */
    width: fill-available;  
  height: 450px;
}

.hs-rss-item-text
{
  position: absolute !important;
  bottom: 5%;
  right: 5%;
  left: 5%;
  background-color: #fff;
  min-height:100px;
  padding: 1.25em;
  text-align: left;
  font-size:16px;
  color:#191919;
  font-weight:600;
}

.hs-rss-item-text .hs-rss-byline {
  text-transform: uppercase;
  color: #F46419;
}

.hs-rss-item.hs-with-featured-image .hs-rss-item-text .hs-rss-description p
{
  font-size:14px;
  padding-top: 10px;
}

.boxed-h2-smaller {
  font-size: 1.8rem;
}

@media (max-width: 640px) {
    .listing-features-container {
        padding-top: 20px !important;
    }
}