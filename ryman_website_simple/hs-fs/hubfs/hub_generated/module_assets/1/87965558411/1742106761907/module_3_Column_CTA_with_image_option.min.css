.cta-container {
  min-height: 300px;
  display: flex;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 25px;
}

.cta-content {
  padding: 30px;
  text-align: left;
  width: 100%;
}

.cta-content img {
  object-fit: cover;
  max-height: 300px;
  width: 100%;
  padding-bottom: 20px;
}

@media (max-width: 1024px) {
.cta-content {
  padding: 10px !important;
}

@media (max-width: 767px) {
.cta-container {
  max-width: 100%;
  display: block;
}
}