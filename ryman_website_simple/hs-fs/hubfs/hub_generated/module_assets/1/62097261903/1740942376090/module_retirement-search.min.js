var module_62097261903=(customElements.define("retirement-search",class extends HTMLElement{constructor(){super(),this.regionElement=this.querySelector(".js-region-button").nextElementSibling,this.livingCareOptions=this.querySelector("multiselect-dropdown"),this.regions=Array.from(this.querySelectorAll(".retirement-module__region")),this.selectedLocation=this.getAttribute("location"),this.isIsolatedModule=this.getAttribute("is_isolated_module"),this.isRegionListOpen=!1,this.initRegionEvents(),this.handleRegionListClose=e=>{console.log(this.isRegionListOpen),this.isRegionListOpen&&!this.regionElement.parentNode.contains(e.target)&&this.closeRegionInput()},this.livingCareOptions.addEventListener("update",(e=>{this.selectedTypes=e.detail.map((item=>item.value)),this.notifyExternalElements()}))}closeRegionInput(){const button=this.querySelector(".js-region-button");button.classList.remove("retirement-module__label--active"),button.nextElementSibling.classList.add("retirement-module__region-list--hidden"),document.body.removeEventListener("click",this.handleRegionListClose)}showRegionDropdown(){}initRegionEvents(){const button=this.querySelector(".js-region-button");if(button.nextElementSibling.classList.add("retirement-module__region-list--hidden"),this.selectedLocation&&""!==this.selectedLocation){const activeRegionElement=this.regions.find((option=>option.firstElementChild.dataset.location===this.selectedLocation));activeRegionElement&&(button.innerText=activeRegionElement.innerText,this.setSelectedRegion(activeRegionElement))}button.addEventListener("click",(e=>{e.currentTarget.classList.contains("retirement-module__label--active")?(e.currentTarget.setAttribute("aria-expanded",!1),this.closeRegionInput(),this.isRegionListOpen=!1):(document.body.addEventListener("click",this.handleRegionListClose),e.currentTarget.setAttribute("aria-expanded",!0),this.isRegionListOpen=!0,e.target.classList.add("retirement-module__label--active"),e.target.nextElementSibling.classList.remove("retirement-module__region-list--hidden")),this.isRegionListOpen&&e.target.nextElementSibling.firstElementChild.firstElementChild.focus()})),button.nextElementSibling.addEventListener("click",(e=>{if(e.isIsolatedModule)return e.preventDefault(),void(this.selectedLocation=e.target.getAttribute("href"));if(console.log(e.currentTarget),"A"!==e.target.tagName)return;e.preventDefault();const newURL=new URL(window.location);newURL.pathname=e.target.getAttribute("href"),e.target.dataset.location===this.selectedLocation&&(newURL.pathname="../retirement-villages/"),window.location=newURL}))}initLivingOptions(){this.querySelector(".js-button-living-options")}setSelectedRegion(selected){for(const region of this.regions)region.classList.remove("retirement-module__region--is-active");selected.classList.add("retirement-module__region--is-active")}notifyExternalElements(){const map=document.querySelector("village-map"),cards=document.querySelector("village-cards"),clearFilterButton=document.querySelector(".js-clear-button");clearFilterButton&&(this.selectedTypes&&this.selectedTypes.length?clearFilterButton.classList.remove("village-buttons__button--hidden"):clearFilterButton.classList.add("village-buttons__button--hidden")),map&&map.updateFilters(this.selectedTypes),cards&&cards.updateFilters(this.selectedTypes)}}),void customElements.define("multiselect-dropdown",class extends HTMLElement{constructor(){super(),this.button=this.querySelector("button"),this.dropdown=this.querySelector(".js-options"),this.options=this.querySelectorAll("input"),this.isVisible=!1,this.checked=[],this.handleClickOutside=e=>{console.log(e.target),this.isVisible&&!this.dropdown.contains(e.target)&&e.target!==this.button&&this.hideDropdown()}}hydrateFromURL(){}showDropdown(){this.dropdown.classList.remove("multiselect-dropdown__options--hidden"),this.button.classList.add("multiselect-dropdown__label--active"),this.isVisible=!0}hideDropdown(){this.dropdown.classList.add("multiselect-dropdown__options--hidden"),this.button.classList.remove("multiselect-dropdown__label--active"),this.isVisible=!1}updateState(){this.checked=Array.from(this.options).filter((item=>item.checked)).map((item=>({name:item.getAttribute("name"),value:item.getAttribute("value")}))),this.dispatchEvent(new CustomEvent("update",{detail:this.checked})),0!==this.checked.length?this.button.innerHTML=`Living and Care Options <span>(${this.checked.length})</span>`:this.button.innerHTML="Living and Care Options"}updateURL(){const existingURL=new URL(window.location),params=new URLSearchParams(existingURL.search);params.delete("type");for(const option of this.checked)params.append(option.name,option.value);existingURL.search=params,window.history.replaceState({},"",existingURL.toString())}initListeners(){this.addEventListener("change",(e=>{e.target,this.updateState(),this.updateURL()})),document.body.addEventListener("click",this.handleClickOutside)}connectedCallback(){this.dropdown.classList.add("multiselect-dropdown__options--hidden"),this.isVisible=!1,this.button.addEventListener("click",(e=>{e.current,this.isVisible?this.hideDropdown():this.showDropdown()})),this.initListeners()}}));
//# sourceURL=https://4527941.fs1.hubspotusercontent-na1.net/hubfs/4527941/hub_generated/module_assets/1/62097261903/1740942376090/module_retirement-search.js