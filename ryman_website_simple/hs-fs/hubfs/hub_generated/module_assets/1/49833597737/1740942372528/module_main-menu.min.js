var module_49833597737=void function(){document.querySelector(".js-search");let header=document.querySelector(".js-header");document.querySelector(".js-content-search"),document.querySelector(".js-close-content").addEventListener("click",(()=>{console.log("Close content");const element=document.querySelector(".js-content-search"),elementPhone=document.querySelector(".js-content-phone");header.classList.remove("header--show-content"),header.classList.remove("header--is-search"),element.classList.remove("nav__content--is-visible"),elementPhone.classList.remove("nav__content--is-visible")})),document.querySelector(".js-button-search").addEventListener("click",(e=>{const mq=window.matchMedia("(min-width: 980px)"),element=document.querySelector(".js-content-search");if(mq.matches){const buttonPosition=e.currentTarget.getBoundingClientRect();element.classList.add("nav__content--is-visible");const widthofSearchInput=400;element.style.left=buttonPosition.left+e.currentTarget.offsetWidth-widthofSearchInput+"px";const input=element.querySelector("input"),searchInputElement=document.querySelector(".js-search-input");input.focus(),input.addEventListener("focusout",(e=>{searchInputElement.contains(e.currentTarget)||element.classList.remove("nav__content--is-visible")}))}else element.style.left="0px",header.classList.add("header--is-search"),header.classList.add("header--show-content"),element.classList.add("nav__content--is-visible")}));let currentlyFocused=document.querySelector(".js-nav-link");document.querySelector(".js-nav-button").addEventListener("click",(e=>{console.log("Clicked nav"),e.preventDefault(),header.classList.contains("header--is-open")?(e.currentTarget.classList.remove("is-active"),header.classList.remove("header--is-open"),document.body.classList.remove("js-nav-is-active")):(e.currentTarget.classList.add("is-active"),header.classList.add("header--is-open"),document.body.classList.add("js-nav-is-active"),currentlyFocused.focus())}));let isFirst=!0;document.querySelector(".js-main-menu").addEventListener("click",(e=>{window.matchMedia("(min-width: 980px)").matches||(function(){const elements=document.querySelectorAll(".main-menu__item--is-active");for(const element of elements)element.style.display="none",element.classList.remove("main-menu__item--is-active"),element.style.display="block"}(),e.target.parentNode.classList.contains("main-menu__item--has-children")&&(e.preventDefault(),e.target.parentNode.classList.add("main-menu__item--is-active"),isFirst||(currentlyFocused.parentNode.classList.remove("main-menu__item--is-active"),currentlyFocused=e.target),isFirst=!1))})),document.querySelector(".js-button-phone").addEventListener("click",(()=>{const element=document.querySelector(".js-content-phone");console.log(element),header.classList.add("header--show-content"),element.classList.add("nav__content--is-visible")}));const closeSearchButton=document.querySelector(".js-search-input .search-input__close-btn"),searchInputElement=document.querySelector(".js-search-input .search-input__input");document.querySelector(".js-search-input .search-input__input").addEventListener("keyup",(e=>{e.currentTarget.value?closeSearchButton.classList.add("search-input__close-btn--is-visible"):closeSearchButton.classList.remove("search-input__close-btn--is-visible")})),closeSearchButton.addEventListener("click",(e=>{header.classList.remove("header--show-content"),header.classList.remove("header--is-search"),searchInputElement.value="",closeSearchButton.classList.remove("search-input__close-btn--is-visible"),document.querySelector(".js-content-search").classList.remove("nav__content--is-visible"),e.preventDefault()})),window.matchMedia("(min-width: 980px)"),document.querySelectorAll(".js-nav-link"),document.querySelector("#site-search").addEventListener("keydown",(e=>{13===e.keyCode&&(window.location=`/hs-search-results?term=${e.currentTarget.value}`)})),new autoComplete({debounce:400,maxResults:10,selector:"#site-search",placeHolder:"Search...",searchEngine:"loose",threshold:3,data:{src:async term=>{try{const data=await fetch(`/_hcms/search?term=${term}&limit=3&autocomplete=true&analytics=true&type=SITE_PAGE&type=BLOG_POST&type=LISTING_PAGE`);return(await data.json()).results.map((item=>({title:item.title,description:item.description,url:item.url})))}catch(err){return err}},keys:["title","description"]}}).input.addEventListener("selection",(event=>{console.log(event.detail),window.location=event.detail.selection.value.url}))}();
//# sourceURL=https://4527941.fs1.hubspotusercontent-na1.net/hubfs/4527941/hub_generated/module_assets/1/49833597737/1740942372528/module_main-menu.js