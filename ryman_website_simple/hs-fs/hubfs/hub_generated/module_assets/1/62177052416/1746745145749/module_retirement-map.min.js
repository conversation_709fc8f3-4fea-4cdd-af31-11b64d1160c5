var module_62177052416=function(){let initCalled;const callbackPromise=new Promise((r=>window.__initGoodMap=r));function GooglePopup(...args){google.maps.Map.prototype.panToWithOffset=function(latlng,offsetX,offsetY){var map=this,ov=new google.maps.OverlayView;ov.onAdd=function(){var proj=this.getProjection(),aPoint=proj.fromLatLngToContainerPixel(latlng);aPoint.x=aPoint.x+offsetX,aPoint.y=aPoint.y+offsetY,map.panTo(proj.fromContainerPixelToLatLng(aPoint))},ov.draw=function(){},ov.setMap(this)};class Popup extends google.maps.OverlayView{position;containerDiv;contentDiv;data;visible=!1;constructor(position,content,data){super(),this.position=position,console.log(this.mapInstance),console.log(this),this.contentDiv=document.createElement("div"),this.contentDiv.classList.add("popup-content"),this.contentDiv.innerHTML=content;const bubbleAnchor=document.createElement("div");bubbleAnchor.classList.add("popup-bubble-anchor"),bubbleAnchor.appendChild(this.contentDiv),this.containerDiv=document.createElement("div"),this.containerDiv.classList.add("popup-container"),this.containerDiv.appendChild(bubbleAnchor);const closeButton=document.createElement("button");closeButton.classList.add("popup-bubble__close-btn"),closeButton.classList.add("js-popup-close-btn");const span=document.createElement("span");span.innerHTML="&times;",span.setAttribute("aria-hidden","true"),closeButton.appendChild(span),closeButton.setAttribute("aria-label","Close popup"),this.containerDiv.appendChild(closeButton),this.containerDiv.style.display="none",Popup.preventMapHitsAndGesturesFrom(this.containerDiv)}setContent(content){this.contentDiv.innerHTML=content}onAdd(){this.getPanes().floatPane.appendChild(this.containerDiv),google.maps.event.addDomListener(this.containerDiv,"click",(e=>{if(e.target.classList.contains("js-popup-close-btn"))return e.preventDefault(),void this.close();window.location=this.data.link}))}onRemove(){this.containerDiv.parentElement&&this.containerDiv.parentElement.removeChild(this.containerDiv)}updatePosition(){const divPosition=this.getProjection().fromLatLngToDivPixel(this.position);this.containerDiv.style.left=divPosition.x+"px",this.containerDiv.style.top=divPosition.y+"px"}close(){this.containerDiv.style.display="none",this.visible=!1}open(options){const{anchor:anchor,shouldFocus:shouldFocus,data:data}=options;this.data=data,this.mapInstance=options.map,this.setMap(this.mapInstance),this.visible=!0,this.position=anchor.getPosition(),this.updatePosition()}draw(){if(!this.visible)return!1;const divPosition=this.getProjection().fromLatLngToDivPixel(this.position),display=Math.abs(divPosition.x)<4e3&&Math.abs(divPosition.y)<4e3?"block":"none";"block"===display&&(this.containerDiv.style.left=divPosition.x+"px",this.containerDiv.style.top=divPosition.y+"px"),this.containerDiv.style.display!==display&&(this.containerDiv.style.display=display)}}return new Popup(...args)}customElements.define("village-map",class extends HTMLElement{constructor(){super(),this.map=null,this.apiKey="AIzaSyB1qB2ZgMV0HN72ebG_3EUNxvT2NPUkdCs",this.zoom=parseFloat(this.getAttribute("zoom"))?parseFloat(this.getAttribute("zoom")):6,this.latitude=null,this.longitude=null,this.villages=JSON.parse(this.querySelector("script").textContent),this.filteredVillages=[...this.villages],this.markers=[],this.selectedRegion;let center=this.getAttribute("center");if(center&&center.split(",")?.length){let split=center.split(",");center={lat:parseFloat(split[0]),lng:parseFloat(split[1])}}else center={lat:-40.848461,lng:174.763336};this.mapOptions={streetViewControl:!1,mapTypeControl:!1,fullscreenControl:!1,zoom:this.zoom,center:center,mapId:"e16ebdb8eff5b56"},this.markerCluster=null,this.clusterStyles=[{width:30,height:30,className:"cluster-icon-1"},{width:40,height:40,className:"cluster-icon-2"},{width:50,height:50,className:"cluster-icon-3"}]}setRegion(region){this.selectedRegion=region}createMarkers(){console.log(this.filteredVillages);for(const village of this.filteredVillages){if(!village?.location_lat||!village?.location_lng)continue;if(village.hidden)continue;const{village_name:village_name,village_address:village_address,village_url:village_url,status:status}=village,svg=this.svgMarker(village.label&&village.label.colour?village.label.colour:"#f37121"),latLng=new google.maps.LatLng(village.location_lat,village.location_lng);let marker=new google.maps.Marker({position:latLng,icon:svg,title:village.location_name,label:village.location_name});marker.villageId=village.id;let label="";village.label&&village.label.name&&(label=`<div class="info-window__card-label" style="background-color:${village.label.colour}">${village.label.name}</div>`),marker.addListener("click",(()=>{const content=`\n\n              <div class="info-window__card">\n                  ${label}\n                  <div class="info-window__card-image">\n                      <a href="${village.village_url}"><img src="${village.village_image}" alt="${village.village_name}"  class="info-window__card-image" /></a>\n                  </div>\n                    <div class="info-window__card-content">\n                        <div class="info-window__card-intro">${village.region_name}</div>\n                        <h3>${village.village_name}</h3>\n                        <div class="info-window__card-info">\n                            <div class="info-window__card-info-row">\n                                <svg width="16" height="16">\n                                    <use xlink:href="#location-marker-icon" />\n                                </svg>\n                                ${village.village_address}\n                            </div>\n                            <div class="info-window__card-info-row">\n                                <svg width="16" height="16">\n                                    <use xlink:href="#phone-icon" />\n                                </svg>\n                                <a class="info-window__card-info-link" href="callto://${village.sales_phone}">${village.sales_phone}</a>\n                            </div>\n                            <a href="${village.village_url}" class="orange-btn">Learn more</a>\n                        </div>\n                    </div>\n                </div>\n                `;this.googlePopupInstance.setContent(content),this.mapInstance.panToWithOffset(marker.getPosition(),null,-200),this.googlePopupInstance.open({anchor:marker,map:this.mapInstance,shouldFocus:!1,data:{link:village_url}})})),this.markers=[...this.markers,marker]}this.markerCluster=new MarkerClusterer(this.mapInstance,this.markers,{clusterClass:"cluster-icon",styles:this.clusterStyles})}clearMarkers(){this.markers.forEach((marker=>{marker.setMap(null)})),this.markers=[],this.markerCluster.clearMarkers(),this.markerCluster=null}renderMap(){this.googlePopupInstance=new GooglePopup(new google.maps.LatLng(-33.866,151.196),""),this.mapOptions.zoomControlOptions={style:google.maps.ZoomControlStyle.SMALL,position:google.maps.ControlPosition.LEFT},this.mapInstance=new google.maps.Map(this,this.mapOptions),this.googlePopupInstance.setMap(this.mapInstance),this.dispatchEvent(new CustomEvent("google-map-ready",{detail:this.map})),this.createMarkers()}svgMarker(colour){return{url:`data:image/svg+xml;utf-8,%3Csvg width='38px' height='34px' version='1.1' viewBox='0 0 12 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg transform='translate(0)'%3E%3Cpath d='m6 0c3.3137 0 6 2.6863 6 6 0 2.0511-1.3563 4.4123-2.7792 6.3252l-0.35637 0.46842-0.35467 0.4471c-0.05872 0.072594-0.11718 0.14419-0.1753 0.21473l-0.34371 0.41019c-0.056335 0.066112-0.11213 0.13106-0.1673 0.19479l-0.47695 0.53923-0.42778 0.4646-0.36349 0.38107-0.55519 0.55469-0.20576-0.20091-0.34943-0.35378-0.36349-0.38107-0.27897-0.301-0.30307-0.33549-0.32269-0.36734-0.33782-0.39656c-0.057317-0.068383-0.11508-0.13787-0.17319-0.20842l-0.35214-0.43546c-0.059142-0.074536-0.11845-0.15001-0.17783-0.22638l-0.35637-0.46842c-1.4229-1.9128-2.7792-4.2741-2.7792-6.3252 0-3.3137 2.6863-6 6-6z' fill='${colour=encodeURIComponent(colour)}' fill-rule='nonzero'/%3E%3Ccircle id='Oval' cx='6' cy='6' r='3' fill='%2523FFFFFF'/%3E%3C/g%3E%3C/g%3E%3Cellipse cx='6.0023' cy='6.0298' rx='3.0149' ry='3.0424' fill='%23fff' stroke-width='.46341'/%3E%3C/svg%3E`,anchor:new google.maps.Point(19,34),size:new google.maps.Size(38,34),origin:new google.maps.Point(0,0)}}updateFilters(types){this.types=types,this.filteredVillages=[...this.villages],this.types&&this.types.length&&(console.log("clear markers",this.types),this.clearMarkers(),this.filteredVillages=this.filteredVillages.filter((item=>{const livingOptions=item.living_options;return 0===livingOptions.length||this.types.some((t=>livingOptions.includes(t)))})),this.filteredVillages=this.filteredVillages.map((item=>({...item,hidden:!1}))),this.createMarkers())}connectedCallback(){(function(apiKey){if(!initCalled){const script=document.createElement("script");script.src="https://maps.googleapis.com/maps/api/js?"+(apiKey?`key=${apiKey}&`:"")+"callback=__initGoodMap",document.head.appendChild(script),initCalled=!0}return callbackPromise})(this.apiKey).then((()=>{console.log("connected callback"),console.log("google maps has loaded."),this.renderMap()}));const mapKey=document.getElementById("map-key");console.log(mapKey),mapKey&&this.parentNode.appendChild(mapKey.content.cloneNode(!0))}createMapKey(){}})}();
//# sourceURL=https://4527941.fs1.hubspotusercontent-na1.net/hubfs/4527941/hub_generated/module_assets/1/62177052416/1746745145749/module_retirement-map.js