var module_86870414701=function(){var __hs_messages={};i18n_getmessage=function(){return hs_i18n_getMessage(__hs_messages,hsVars.language,arguments)},i18n_getlanguage=function(){return hsVars.language};var url=new URL(window.location);url.pathname="/retirement-villages",url.search="",console.log(url);const newURL=url;customElements.define("retirement-search",class extends HTMLElement{constructor(){super(),this.regionElement=this.querySelector(".js-region-button").nextElementSibling,this.livingCareOptions=this.querySelector("multiselect-dropdown"),this.button=this.querySelector(".js-submit-button"),this.regions=Array.from(this.querySelectorAll(".retirement-module__region")),this.selectedLocation=this.getAttribute("location"),this.isIsolatedModule=this.getAttribute("is_isolated_module"),this.isRegionListOpen=!1,this.initRegionEvents(),this.handleRegionListClose=e=>{this.isRegionListOpen&&!this.regionElement.parentNode.contains(e.target)&&this.closeRegionInput()},this.livingCareOptions.addEventListener("update",(e=>{this.selectedTypes=e.detail.map((item=>item.value))})),this.button.addEventListener("click",(e=>{e.preventDefault(),window.location=newURL,console.log(newURL)}))}closeRegionInput(){const button=this.querySelector(".js-region-button");button.classList.remove("retirement-module__label--active"),button.nextElementSibling.classList.add("retirement-module__region-list--hidden"),document.body.removeEventListener("click",this.handleRegionListClose)}showRegionDropdown(){}initRegionEvents(){const button=this.querySelector(".js-region-button");if(button.nextElementSibling.classList.add("retirement-module__region-list--hidden"),this.selectedLocation&&""!==this.selectedLocation){const activeRegionElement=this.regions.find((option=>option.firstElementChild.dataset.location===this.selectedLocation));activeRegionElement&&(button.innerText=activeRegionElement.innerText,this.setSelectedRegion(activeRegionElement))}button.addEventListener("click",(e=>{e.currentTarget.classList.contains("retirement-module__label--active")?(e.currentTarget.setAttribute("aria-expanded",!1),this.closeRegionInput(),this.isRegionListOpen=!1):(document.body.addEventListener("click",this.handleRegionListClose),e.currentTarget.setAttribute("aria-expanded",!0),this.isRegionListOpen=!0,e.target.classList.add("retirement-module__label--active"),e.target.nextElementSibling.classList.remove("retirement-module__region-list--hidden")),this.isRegionListOpen&&e.target.nextElementSibling.firstElementChild.firstElementChild.focus()})),button.nextElementSibling.addEventListener("click",(e=>{if("A"!==e.target.tagName)return;e.preventDefault(),newURL.pathname=e.target.getAttribute("href"),this.selectedLocation=e.target.dataset.location,this.redirectLocation=newURL;const activeRegionElement=this.regions.find((option=>option.firstElementChild.dataset.location===this.selectedLocation));console.log(activeRegionElement),console.log(this.selectedLocation),activeRegionElement&&(button.innerText=activeRegionElement.innerText,this.setSelectedRegion(activeRegionElement)),this.closeRegionInput()}))}initLivingOptions(){this.querySelector(".js-button-living-options")}setSelectedRegion(selected){for(const region of this.regions)region.classList.remove("retirement-module__region--is-active");selected.classList.add("retirement-module__region--is-active")}}),customElements.define("multiselect-dropdown",class extends HTMLElement{constructor(){super(),this.button=this.querySelector("button"),this.dropdown=this.querySelector(".js-options"),this.options=this.querySelectorAll("input"),this.isVisible=!1,this.checked=[],this.handleClickOutside=e=>{this.isVisible&&!this.dropdown.contains(e.target)&&e.target!==this.button&&this.hideDropdown()}}showDropdown(){this.dropdown.classList.remove("multiselect-dropdown__options--hidden"),this.button.classList.add("multiselect-dropdown__label--active"),this.isVisible=!0}hideDropdown(){this.dropdown.classList.add("multiselect-dropdown__options--hidden"),this.button.classList.remove("multiselect-dropdown__label--active"),this.isVisible=!1}updateState(){this.checked=Array.from(this.options).filter((item=>item.checked)).map((item=>({name:item.getAttribute("name"),value:item.getAttribute("value")}))),this.dispatchEvent(new CustomEvent("update",{detail:this.checked})),0!==this.checked.length?this.button.innerHTML=`Living and Care Options <span>(${this.checked.length})</span>`:this.button.innerHTML="Living and Care Options"}updateURL(){const params=new URLSearchParams(newURL);params.delete("type");for(const option of this.checked)params.append(option.name,option.value);newURL.search=params}initListeners(){this.addEventListener("change",(e=>{e.target;this.updateState(),this.updateURL()})),document.body.addEventListener("click",this.handleClickOutside)}connectedCallback(){this.dropdown.classList.add("multiselect-dropdown__options--hidden"),this.isVisible=!1,this.button.addEventListener("click",(e=>{e.current;this.isVisible?this.hideDropdown():this.showDropdown()})),this.initListeners()}});const defaultSelected=document.querySelectorAll(".default-selected");defaultSelected&&defaultSelected.forEach((ele=>{ele.click()}));const allChecked=document.querySelectorAll(".is-checked");allChecked&&allChecked.forEach((ele=>{const params=new URLSearchParams(newURL);params.delete("type"),params.append(ele.name,ele.value),newURL.search=params,console.log(newURL)}))}();