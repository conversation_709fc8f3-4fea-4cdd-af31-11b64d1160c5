var module_74533670563=function(){var __hs_messages={};function sameHeights(selector){selector=selector||'[data-key="sameHeights"]';var query=document.querySelectorAll(selector),elements=query.length,max=0;if(elements){for(;elements--;){(element=query[elements]).clientHeight>max&&(max=element.clientHeight)}for(elements=query.length;elements--;){var element;(element=query[elements]).style.height=max+"px"}}}i18n_getmessage=function(){return hs_i18n_getMessage(__hs_messages,hsVars.language,arguments)},i18n_getlanguage=function(){return hsVars.language},"addEventListener"in window&&(window.addEventListener("resize",(function(){sameHeights()})),window.addEventListener("load",(function(){sameHeights()})))}();