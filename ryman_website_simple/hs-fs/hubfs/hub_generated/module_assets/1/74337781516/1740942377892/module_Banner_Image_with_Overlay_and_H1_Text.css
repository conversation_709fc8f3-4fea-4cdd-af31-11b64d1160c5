.full-width-image-with-content .custom-content {
  max-width: 35% !important;
  background-color: rgba(255, 255, 255, 0.0) !important;
  padding: 3em 0em !important;
  text-align: left;
}

.full-width-image-with-content .page-center {
  padding-left: 20px !important;
}
.overlay-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.45), rgba(0, 0, 0, 0.30), rgba(0, 0, 0, 0.0));  
}

@media (max-width:1360px) and (min-width:767px){
  .full-width-image-with-content .custom-content {
  position: absolute !important;
  max-width: 80% !important;
  padding: 2em 0em 1em !important;
  background-color: none !important;
}
  .full-width-image-with-content .custom-content h1 {
  font-size: 3em;
}

}
  
@media (max-width:767px){
  .full-width-image-with-content .custom-content {
  position: absolute !important;
  max-width: 80% !important;
  padding: 2em 0em 0.75em !important;
  background-color: none !important;
}

}
  }
}

