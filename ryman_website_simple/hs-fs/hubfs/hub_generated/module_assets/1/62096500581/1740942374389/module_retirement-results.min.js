var module_62096500581=void customElements.define("village-cards",class extends HTMLElement{constructor(){super(),this.cards=Array.from(this.querySelectorAll(".card")),this.filteredCards=[...this.cards],this.shouldRender=!1,this.noResultsMessage=this.querySelector(".js-no-results"),this.showResults=8,this.resultsPerPage=8}connectedCallback(){const livingOptions=this.getAttribute("livingOptions");livingOptions&&livingOptions.split(",").length&&(this.types=this.getAttribute("livingOptions").split(",")),this.watchForUpdates(),this.updateFiltersLocally(),this.initPagination(),this.render()}hideAll(){for(const card of this.cards)card.style.display="none"}render(){this.hideAll();let totalVisible=0,paginatedCards=this.filteredCards.slice(0,this.showResults);for(const card of paginatedCards)card.style.display="flex",totalVisible++;0===totalVisible?this.noResultsMessage.classList.add("village-cards__message--visible"):this.noResultsMessage.classList.remove("village-cards__message--visible"),this.notifyResultCountElements(),this.renderPaginationButton()}notifyResultCountElements(){document.querySelectorAll(".js-results-count").forEach((item=>item.innerText=this.filteredCards.length))}updateFiltersLocally(){this.filteredCards=[...this.cards],this.types&&this.types.length&&(this.filteredCards=this.filteredCards.filter((item=>{const livingOptions=item.dataset.livingOptions.split(",");return 0===livingOptions.length||this.types.some((t=>livingOptions.includes(t)))})))}updateFilters(types){this.types=types,console.log(this.types),this.updateFiltersLocally(),this.render()}renderPaginationButton(){this.querySelector(".js-pagination-button");const paginationContainer=this.querySelector(".js-pagination");console.log(this.filteredCards.length),this.filteredCards.length>this.showResults?paginationContainer.classList.remove("village-cards__pagination--hidden"):paginationContainer.classList.add("village-cards__pagination--hidden"),this.filteredCards.length>this.showResults?paginationContainer.classList.remove("village-cards__pagination--hidden"):paginationContainer.classList.add("village-cards__pagination--hidden")}initPagination(){const button=this.querySelector(".js-pagination-button"),paginationContainer=this.querySelector(".js-pagination");console.log(this.filteredCards.length),this.renderPaginationButton(),button.addEventListener("click",(e=>{e.preventDefault(),this.showResults+=8,this.showResults>=this.filteredCards.length&&(this.showResults=this.filteredCards.length,paginationContainer.classList.add("village-cards__pagination--hidden")),this.render()}))}getFiltersFromURL(){const url=new URL(window.location),types=new URLSearchParams(url.search).getAll("type");types&&(console.log(types),this.types=types)}watchForUpdates(){this.getFiltersFromURL()}attributeChangedCallback(name,oldValue,newValue){this.location=newValue,console.log(this.location),this.updateFilters(),this.render()}});
//# sourceURL=https://4527941.fs1.hubspotusercontent-na1.net/hubfs/4527941/hub_generated/module_assets/1/62096500581/1740942374389/module_retirement-results.js