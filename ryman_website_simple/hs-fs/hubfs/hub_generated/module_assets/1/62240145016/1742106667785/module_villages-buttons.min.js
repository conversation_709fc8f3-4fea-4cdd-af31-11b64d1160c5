var module_62240145016=function(){var __hs_messages={};i18n_getmessage=function(){return hs_i18n_getMessage(__hs_messages,hsVars.language,arguments)},i18n_getlanguage=function(){return hsVars.language};let mapContainer=document.querySelector(".js-village-map"),cardContainer=document.querySelector(".js-village-cards");const element=document.querySelector(".js-toggle-view");let params=new URLSearchParams(window.location.search),view=params.get("view");function showList(updateUrl=!1){mapContainer.style.display="none",cardContainer.style.display="block",element.innerText="Map view",element.classList.add("view-toggle--map"),element.classList.remove("view-toggle--list"),updateUrl&&params.set("view","list")}function showMap(updateUrl){mapContainer.style.display="block",cardContainer.style.display="none",element.innerText="List view",element.classList.add("view-toggle--list"),element.classList.remove("view-toggle--map"),updateUrl&&params.set("view","map")}"map"===view?showMap(!1):showList(!1),element.addEventListener("click",(e=>{e.preventDefault();const url=new URL(window.location);if(params=new URLSearchParams(url.search),view="map"===view?"list":"map","list"===view)showList(!0);else showMap(!0);url.search=params,window.history.replaceState({},"",url.toString())}))}();