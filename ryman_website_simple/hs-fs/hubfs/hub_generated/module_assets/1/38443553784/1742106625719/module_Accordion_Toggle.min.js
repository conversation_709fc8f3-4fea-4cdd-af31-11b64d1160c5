var module_1883428=function(){var __hs_messages={};function buildAccordion(el){var accordion=el,accordionItems=accordion.children,oneAtTime=accordion.getAttribute("data-sync"),clickBind=function(){accordion.addEventListener("click",(function(e){var ex;e.target&&"BUTTON"==e.target.nodeName&&(oneAtTime&&(ex=e.target.parentNode,Array.prototype.forEach.call(accordionItems,(function(el,i){el!=ex&&el.setAttribute("aria-expanded","false")}))),function(el){"true"==el.getAttribute("aria-expanded")?el.setAttribute("aria-expanded","false"):el.setAttribute("aria-expanded","true")}(e.target.parentNode))}))};clickBind()}i18n_getmessage=function(){return hs_i18n_getMessage(__hs_messages,hsVars.language,arguments)},i18n_getlanguage=function(){return hsVars.language};var accordions=document.querySelectorAll(".hs-accordion");Array.prototype.forEach.call(accordions,(function(el){buildAccordion(el)}))}();