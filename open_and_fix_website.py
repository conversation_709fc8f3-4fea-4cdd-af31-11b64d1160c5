#!/usr/bin/env python3
"""
Open and Fix Website Script
This script:
1. Opens the downloaded website in a browser
2. Runs the link fixer to download missing resources
3. Converts all links to work locally
4. Provides a local server for testing
"""

import os
import sys
import webbrowser
import subprocess
import time
from pathlib import Path
import http.server
import socketserver
import threading
from link_fixer import LinkFixer


class LocalWebsiteManager:
    def __init__(self, website_dir='ryman_website_simple'):
        self.website_dir = Path(website_dir)
        self.port = 8080
        self.server = None
        self.server_thread = None
    
    def check_website_exists(self):
        """Check if the website directory exists and has content"""
        if not self.website_dir.exists():
            print(f"❌ Website directory '{self.website_dir}' not found!")
            print("Please run the spider first:")
            print("  ./run_full_spider.sh")
            return False
        
        html_files = list(self.website_dir.glob('*.html'))
        if not html_files:
            print(f"❌ No HTML files found in '{self.website_dir}'!")
            print("Please run the spider first to download the website.")
            return False
        
        print(f"✅ Found website with {len(html_files)} HTML files")
        return True
    
    def start_local_server(self):
        """Start a local HTTP server to serve the website"""
        try:
            os.chdir(self.website_dir)
            
            # Find an available port
            for port in range(8080, 8090):
                try:
                    handler = http.server.SimpleHTTPRequestHandler
                    self.server = socketserver.TCPServer(("", port), handler)
                    self.port = port
                    break
                except OSError:
                    continue
            
            if not self.server:
                print("❌ Could not find an available port")
                return False
            
            print(f"🌐 Starting local server on http://localhost:{self.port}")
            
            # Start server in a separate thread
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ Error starting server: {str(e)}")
            return False
    
    def stop_server(self):
        """Stop the local server"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("🛑 Local server stopped")
    
    def open_in_browser(self):
        """Open the website in the default browser"""
        try:
            url = f"http://localhost:{self.port}/index.html"
            print(f"🔗 Opening {url} in browser...")
            webbrowser.open(url)
            return True
        except Exception as e:
            print(f"❌ Error opening browser: {str(e)}")
            return False
    
    def run_link_fixer(self):
        """Run the link fixer to complete the website"""
        print("\n🔧 Running link fixer to complete the website...")
        print("This will:")
        print("  - Scan all HTML files for external links")
        print("  - Download missing pages and resources")
        print("  - Convert absolute URLs to relative URLs")
        print("  - Make the website work completely offline")
        print()
        
        try:
            fixer = LinkFixer(str(self.website_dir))
            fixer.run_complete_fix()
            print("\n✅ Link fixing completed successfully!")
            return True
        except Exception as e:
            print(f"❌ Error running link fixer: {str(e)}")
            return False
    
    def show_website_stats(self):
        """Show statistics about the downloaded website"""
        html_files = list(self.website_dir.rglob('*.html'))
        css_files = list(self.website_dir.rglob('*.css'))
        js_files = list(self.website_dir.rglob('*.js'))
        img_files = list(self.website_dir.rglob('*.jpg')) + \
                   list(self.website_dir.rglob('*.png')) + \
                   list(self.website_dir.rglob('*.gif')) + \
                   list(self.website_dir.rglob('*.svg'))
        
        total_size = sum(f.stat().st_size for f in self.website_dir.rglob('*') if f.is_file())
        
        print("\n📊 Website Statistics:")
        print(f"  HTML files: {len(html_files)}")
        print(f"  CSS files: {len(css_files)}")
        print(f"  JavaScript files: {len(js_files)}")
        print(f"  Image files: {len(img_files)}")
        print(f"  Total size: {total_size / (1024*1024):.1f} MB")
        print(f"  Directory: {self.website_dir.absolute()}")
    
    def create_desktop_shortcut(self):
        """Create a desktop shortcut to open the website"""
        try:
            desktop = Path.home() / 'Desktop'
            if not desktop.exists():
                return False
            
            shortcut_content = f"""#!/bin/bash
cd "{self.website_dir.absolute()}"
python3 -m http.server {self.port} &
sleep 2
open "http://localhost:{self.port}/index.html"
"""
            
            shortcut_path = desktop / 'Ryman_Healthcare_Local.command'
            with open(shortcut_path, 'w') as f:
                f.write(shortcut_content)
            
            os.chmod(shortcut_path, 0o755)
            print(f"🔗 Created desktop shortcut: {shortcut_path}")
            return True
            
        except Exception as e:
            print(f"❌ Could not create desktop shortcut: {str(e)}")
            return False


def main():
    print("🕷️  Ryman Healthcare Website - Local Browser")
    print("=" * 50)
    
    manager = LocalWebsiteManager()
    
    # Check if website exists
    if not manager.check_website_exists():
        sys.exit(1)
    
    # Show current stats
    manager.show_website_stats()
    
    # Ask user what to do
    print("\nWhat would you like to do?")
    print("1. Fix links and complete website download")
    print("2. Open website in browser (current state)")
    print("3. Both - fix links then open browser")
    print("4. Create desktop shortcut")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == '1':
        # Fix links only
        if manager.run_link_fixer():
            print("\n✅ Website is now complete and ready to use!")
            print(f"You can open {manager.website_dir}/index.html in any browser")
    
    elif choice == '2':
        # Open browser only
        if manager.start_local_server():
            manager.open_in_browser()
            print("\n✅ Website opened in browser!")
            print("Press Ctrl+C to stop the server")
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                manager.stop_server()
    
    elif choice == '3':
        # Fix links then open browser
        if manager.run_link_fixer():
            print("\n🌐 Starting local server...")
            if manager.start_local_server():
                manager.open_in_browser()
                print("\n✅ Website fixed and opened in browser!")
                print("Press Ctrl+C to stop the server")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    manager.stop_server()
    
    elif choice == '4':
        # Create desktop shortcut
        manager.create_desktop_shortcut()
    
    else:
        print("Invalid choice. Exiting.")


if __name__ == '__main__':
    main()
