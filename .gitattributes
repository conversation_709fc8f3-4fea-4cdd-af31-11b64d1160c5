## GITAT<PERSON>IBUTES FOR WEB PROJECTS
#
# These settings are for any web project.
#
# Details per file setting:
#   text    These files should be normalized (i.e. convert CRLF to LF).
#   binary  These files are binary and should be left untouched.
#
# Note that binary is a macro for -text -diff.
######################################################################

## AUTO-DETECT
##   Handle line endings automatically for files detected as
##   text and leave all files detected as binary untouched.
##   This will handle all files NOT defined below.
* text=auto

## SOURCE CODE
*.bat      text eol=crlf
*.coffee   text
*.css      text
*.htm      text
*.html     text
*.inc      text
*.ini      text
*.js       text
*.json     text
*.jsx      text
*.less     text
*.od       text
*.onlydata text
*.php      text
*.pl       text
*.py       text
*.rb       text
*.sass     text
*.scm      text
*.scss     text
*.sh       text eol=lf
*.sql      text
*.styl     text
*.tag      text
*.ts       text
*.tsx      text
*.xml      text
*.xhtml    text

## DOCKER
*.dockerignore text
Dockerfile     text

## DOCUMENTATION
*.markdown   text
*.md         text
*.mdwn       text
*.mdown      text
*.mkd        text
*.mkdn       text
*.mdtxt      text
*.mdtext     text
*.txt        text
AUTHORS      text
CHANGELOG    text
CHANGES      text
CONTRIBUTING text
COPYING      text
copyright    text
*COPYRIGHT*  text
INSTALL      text
license      text
LICENSE      text
NEWS         text
readme       text
*README*     text
TODO         text

## TEMPLATES
*.dot        text
*.ejs        text
*.haml       text
*.handlebars text
*.hbs        text
*.hbt        text
*.jade       text
*.latte      text
*.mustache   text
*.njk        text
*.phtml      text
*.tmpl       text
*.tpl        text
*.twig       text

## LINTERS
.babelrc      text
.csslintrc    text
.eslintrc     text
.htmlhintrc   text
.jscsrc       text
.jshintrc     text
.jshintignore text
.prettierrc   text
.stylelintrc  text

## CONFIGS
*.bowerrc       text
*.cnf           text
*.conf          text
*.config        text
.browserslistrc text
.editorconfig   text
.gitattributes  text
.gitconfig      text
.gitignore      text
.htaccess       text
*.npmignore     text
*.yaml          text
*.yml           text
browserslist    text
Makefile        text
makefile        text

## HEROKU
Procfile    text
.slugignore text

## GRAPHICS
*.ai   binary
*.bmp  binary
*.eps  binary
*.gif  binary
*.ico  binary
*.jng  binary
*.jp2  binary
*.jpg  binary
*.jpeg binary
*.jpx  binary
*.jxr  binary
*.pdf  binary
*.png  binary
*.psb  binary
*.psd  binary
*.svg  text
*.svgz binary
*.tif  binary
*.tiff binary
*.wbmp binary
*.webp binary

## AUDIO
*.kar  binary
*.m4a  binary
*.mid  binary
*.midi binary
*.mp3  binary
*.ogg  binary
*.ra   binary

## VIDEO
*.3gpp binary
*.3gp  binary
*.as   binary
*.asf  binary
*.asx  binary
*.fla  binary
*.flv  binary
*.m4v  binary
*.mng  binary
*.mov  binary
*.mp4  binary
*.mpeg binary
*.mpg  binary
*.ogv  binary
*.swc  binary
*.swf  binary
*.webm binary

## ARCHIVES
*.7z  binary
*.gz  binary
*.jar binary
*.rar binary
*.tar binary
*.zip binary

## FONTS
*.ttf   binary
*.eot   binary
*.otf   binary
*.woff  binary
*.woff2 binary

## EXECUTABLES
*.exe binary
*.pyc binary
