#!/usr/bin/env python3
"""
Link Fixer and Website Completer
This script:
1. Scans all HTML files for external links to rymanhealthcare.co.nz
2. Downloads missing pages and resources
3. Converts absolute URLs to relative URLs
4. Ensures the website works completely offline
"""

import os
import re
import requests
import time
from pathlib import Path
from urllib.parse import urljoin, urlparse, urlunparse
from bs4 import BeautifulSoup
import logging
from collections import deque
import tldextract
from config import *


class LinkFixer:
    def __init__(self, website_dir='ryman_website_simple'):
        self.website_dir = Path(website_dir)
        self.base_url = BASE_URL
        self.allowed_domains = ALLOWED_DOMAINS
        
        # Setup session
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': USER_AGENT})
        
        # Tracking
        self.external_links = set()
        self.missing_files = set()
        self.processed_files = set()
        self.downloaded_count = 0
        self.fixed_links_count = 0
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.website_dir / 'link_fixer.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def scan_html_files(self):
        """Scan all HTML files for external links"""
        self.logger.info("Scanning HTML files for external links...")
        
        html_files = list(self.website_dir.rglob('*.html'))
        self.logger.info(f"Found {len(html_files)} HTML files to scan")
        
        for html_file in html_files:
            self.scan_file_for_links(html_file)
        
        self.logger.info(f"Found {len(self.external_links)} external links")
        return self.external_links
    
    def scan_file_for_links(self, html_file):
        """Scan a single HTML file for external links"""
        try:
            with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            soup = BeautifulSoup(content, 'html.parser')
            
            # Find all links
            for tag in soup.find_all(['a', 'link', 'script', 'img', 'iframe']):
                url = None
                if tag.name == 'a' and tag.get('href'):
                    url = tag['href']
                elif tag.name == 'link' and tag.get('href'):
                    url = tag['href']
                elif tag.name == 'script' and tag.get('src'):
                    url = tag['src']
                elif tag.name == 'img' and tag.get('src'):
                    url = tag['src']
                elif tag.name == 'iframe' and tag.get('src'):
                    url = tag['src']
                
                if url and self.is_external_ryman_link(url):
                    self.external_links.add(url)
            
            # Find CSS background images and other URL references
            css_urls = re.findall(r'url\(["\']?(https://www\.rymanhealthcare\.co\.nz[^"\')\s]+)["\']?\)', content)
            for url in css_urls:
                self.external_links.add(url)
                
        except Exception as e:
            self.logger.error(f"Error scanning {html_file}: {str(e)}")
    
    def is_external_ryman_link(self, url):
        """Check if URL is an external Ryman Healthcare link"""
        if not url.startswith('http'):
            return False
        
        try:
            parsed = urlparse(url)
            return parsed.netloc in ['www.rymanhealthcare.co.nz', 'rymanhealthcare.co.nz']
        except:
            return False
    
    def download_missing_resources(self):
        """Download all missing external resources"""
        self.logger.info(f"Downloading {len(self.external_links)} missing resources...")
        
        for url in self.external_links:
            if self.should_download_url(url):
                self.download_resource(url)
                time.sleep(DOWNLOAD_DELAY)  # Be respectful
    
    def should_download_url(self, url):
        """Check if we should download this URL"""
        local_path = self.url_to_local_path(url)
        return not local_path.exists()
    
    def download_resource(self, url):
        """Download a single resource"""
        try:
            self.logger.info(f"Downloading: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            local_path = self.url_to_local_path(url)
            local_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save content
            content_type = response.headers.get('content-type', '').lower()
            if 'text/' in content_type or 'application/json' in content_type:
                with open(local_path, 'w', encoding='utf-8', errors='ignore') as f:
                    f.write(response.text)
            else:
                with open(local_path, 'wb') as f:
                    f.write(response.content)
            
            self.logger.info(f"Saved: {local_path}")
            self.downloaded_count += 1
            
        except Exception as e:
            self.logger.error(f"Failed to download {url}: {str(e)}")
    
    def url_to_local_path(self, url):
        """Convert URL to local file path"""
        parsed = urlparse(url)
        path = parsed.path
        
        if not path or path == '/':
            path = '/index.html'
        elif not Path(path).suffix and not path.endswith('/'):
            if '.' not in Path(path).name:
                path += '/index.html'
            else:
                path += '.html'
        elif path.endswith('/'):
            path += 'index.html'
        
        path = path.lstrip('/')
        return self.website_dir / path
    
    def fix_all_links(self):
        """Fix all external links in HTML files to use local paths"""
        self.logger.info("Converting external links to local paths...")
        
        html_files = list(self.website_dir.rglob('*.html'))
        
        for html_file in html_files:
            self.fix_links_in_file(html_file)
        
        self.logger.info(f"Fixed {self.fixed_links_count} links in {len(html_files)} files")
    
    def fix_links_in_file(self, html_file):
        """Fix external links in a single HTML file"""
        try:
            with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            original_content = content
            
            # Fix all Ryman Healthcare URLs
            for external_url in self.external_links:
                if external_url in content:
                    relative_path = self.convert_to_relative_path(external_url, html_file)
                    content = content.replace(external_url, relative_path)
                    self.fixed_links_count += 1
            
            # Save if content changed
            if content != original_content:
                with open(html_file, 'w', encoding='utf-8', errors='ignore') as f:
                    f.write(content)
                self.logger.debug(f"Fixed links in: {html_file}")
                
        except Exception as e:
            self.logger.error(f"Error fixing links in {html_file}: {str(e)}")
    
    def convert_to_relative_path(self, external_url, current_file):
        """Convert external URL to relative path from current file"""
        try:
            # Get the local path for the external URL
            target_path = self.url_to_local_path(external_url)
            
            # Calculate relative path from current file to target
            current_dir = current_file.parent
            relative_path = os.path.relpath(target_path, current_dir)
            
            # Convert backslashes to forward slashes for web compatibility
            relative_path = relative_path.replace('\\', '/')
            
            return relative_path
            
        except Exception as e:
            self.logger.error(f"Error converting {external_url} to relative path: {str(e)}")
            return external_url  # Return original if conversion fails
    
    def create_index_page(self):
        """Create a simple index page to browse the website"""
        index_content = """<!DOCTYPE html>
<html>
<head>
    <title>Ryman Healthcare - Local Copy</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #f37121; }
        .info { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat { background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #f37121; }
    </style>
</head>
<body>
    <h1>🏠 Ryman Healthcare - Local Website Copy</h1>
    
    <div class="info">
        <p><strong>Welcome to your local copy of the Ryman Healthcare website!</strong></p>
        <p>This website has been downloaded and optimized to work completely offline.</p>
    </div>
    
    <div class="stats">
        <div class="stat">
            <strong>Pages Downloaded</strong><br>
            """ + str(len(list(self.website_dir.rglob('*.html')))) + """
        </div>
        <div class="stat">
            <strong>Resources Fixed</strong><br>
            """ + str(self.fixed_links_count) + """
        </div>
        <div class="stat">
            <strong>Files Downloaded</strong><br>
            """ + str(self.downloaded_count) + """
        </div>
    </div>
    
    <h2>🔗 Quick Links</h2>
    <ul>
        <li><a href="index.html">Homepage</a></li>
        <li><a href="retirement-villages/index.html">Retirement Villages</a></li>
        <li><a href="living-and-care-options/index.html">Living Options</a></li>
        <li><a href="about-us/index.html">About Ryman</a></li>
        <li><a href="investors/index.html">Investors</a></li>
    </ul>
    
    <div class="info">
        <p><strong>Note:</strong> Some external links (like careers portal) may still point to the original website as they are hosted on different domains.</p>
    </div>
</body>
</html>"""
        
        with open(self.website_dir / 'local_index.html', 'w', encoding='utf-8') as f:
            f.write(index_content)
        
        self.logger.info("Created local index page: local_index.html")
    
    def run_complete_fix(self):
        """Run the complete link fixing process"""
        self.logger.info("Starting complete website link fixing process...")
        
        # Step 1: Scan for external links
        self.scan_html_files()
        
        # Step 2: Download missing resources
        self.download_missing_resources()
        
        # Step 3: Fix all links to be relative
        self.fix_all_links()
        
        # Step 4: Create index page
        self.create_index_page()
        
        self.logger.info("Link fixing process completed!")
        self.logger.info(f"Total external links found: {len(self.external_links)}")
        self.logger.info(f"Total resources downloaded: {self.downloaded_count}")
        self.logger.info(f"Total links fixed: {self.fixed_links_count}")


def main():
    fixer = LinkFixer()
    fixer.run_complete_fix()


if __name__ == '__main__':
    main()
