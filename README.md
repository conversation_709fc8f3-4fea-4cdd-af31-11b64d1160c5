# Ryman Healthcare Website Spider

A comprehensive web spider to download the entire Ryman Healthcare website (https://www.rymanhealthcare.co.nz) to your local machine.

## Features

- **Two Spider Options**: Choose between a full-featured Scrapy spider or a simpler requests-based spider
- **Respectful Crawling**: Implements delays and follows robots.txt
- **Complete Website Download**: Downloads HTML pages, CSS, JavaScript, images, PDFs, and other resources
- **Local File Structure**: Maintains the website's directory structure locally
- **Progress Tracking**: Shows download progress and saves failed URLs for retry
- **Configurable**: Easily adjust crawling parameters like max pages and delays

## Quick Start

### Option 1: One-Click Download (Easiest)

```bash
# Run the complete download script
./run_full_spider.sh
```

### Option 2: Test First (Recommended)

```bash
# Test with virtual environment
source spider_env/bin/activate && python3 test_spider.py

# Then run full download
source spider_env/bin/activate && python3 simple_spider.py
```

### Option 3: Manual Setup

```bash
# Create virtual environment
python3 -m venv spider_env
source spider_env/bin/activate

# Install requirements
pip install -r requirements.txt

# Run spider
python3 simple_spider.py
```

## Spider Options

### Simple Spider (`simple_spider.py`)
- Uses `requests` and `BeautifulSoup`
- Easier to understand and modify
- Good for smaller websites or learning
- Default max pages: 500 (configurable)
- Output directory: `./ryman_website_simple/`

### Scrapy Spider (`ryman_spider.py`)
- Uses the Scrapy framework
- More efficient for large websites
- Advanced features like auto-throttling
- Better handling of concurrent requests
- Output directory: `./ryman_website/`

## Configuration

### Simple Spider Configuration
Edit `simple_spider.py` to modify:
- `max_pages`: Maximum number of pages to crawl (default: 500)
- `download_extensions`: File types to download
- Delay between requests (default: 1 second)

### Scrapy Spider Configuration
Edit the `custom_settings` in `ryman_spider.py`:
- `DOWNLOAD_DELAY`: Delay between requests
- `CONCURRENT_REQUESTS`: Number of concurrent requests
- `AUTOTHROTTLE_*`: Auto-throttling settings

## Output Structure

The spider creates a local directory structure that mirrors the website:

```
ryman_website_simple/  (or ryman_website/)
├── index.html
├── retirement-villages/
│   ├── index.html
│   ├── auckland/
│   └── ...
├── living-and-care-options/
├── about-us/
├── static/
│   ├── css/
│   ├── js/
│   └── images/
├── failed_urls.txt      # URLs that failed to download
└── visited_urls.txt     # All visited URLs
```

## Monitoring Progress

Both spiders provide progress information:
- Number of pages visited
- Number of resources downloaded
- Failed downloads (saved to `failed_urls.txt`)
- Real-time status updates

## Troubleshooting

### Common Issues

1. **Permission Errors**: Make sure you have write permissions in the current directory
2. **Network Timeouts**: The spider will retry failed downloads and save them to `failed_urls.txt`
3. **Memory Usage**: For large sites, the simple spider uses less memory than Scrapy

### Retry Failed Downloads

If some URLs fail, you can retry them by modifying the spider to read from `failed_urls.txt`.

### Adjusting Crawl Speed

- **Faster**: Reduce `DOWNLOAD_DELAY` or sleep time
- **Slower**: Increase delays to be more respectful to the server

## Ethical Considerations

- The spider respects `robots.txt`
- Implements reasonable delays between requests
- Only downloads from the specified domain
- Does not overwhelm the server with requests

## Requirements

- Python 3.7+
- See `requirements.txt` for package dependencies

## License

This tool is for educational and personal use. Please respect the website's terms of service and copyright.
