#!/bin/bash
# Ryman Healthcare Website Spider - Full Download Script

echo "🕷️  Ryman Healthcare Website Spider"
echo "=================================="
echo ""

# Check if virtual environment exists
if [ ! -d "spider_env" ]; then
    echo "Creating virtual environment..."
    python3 -m venv spider_env
    echo "Installing dependencies..."
    source spider_env/bin/activate
    pip install -r requirements.txt
else
    echo "Activating virtual environment..."
    source spider_env/bin/activate
fi

echo ""
echo "Starting full website download..."
echo "This will download the entire Ryman Healthcare website."
echo "Press Ctrl+C to stop at any time."
echo ""

# Run the spider
python3 simple_spider.py

echo ""
echo "✅ Download completed!"
echo "Check the 'ryman_website_simple' directory for all downloaded content."

# Play completion sound
afplay /System/Library/Sounds/Glass.aiff 2>/dev/null || true
